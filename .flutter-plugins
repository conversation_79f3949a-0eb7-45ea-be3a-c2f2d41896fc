# This is a generated file; do not edit or check into version control.
app_links=/Users/<USER>/.pub-cache/hosted/pub.dev/app_links-6.3.2/
app_links_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/app_links_linux-1.0.3/
app_links_web=/Users/<USER>/.pub-cache/hosted/pub.dev/app_links_web-1.0.4/
cloud_firestore=/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-5.5.0/
cloud_firestore_web=/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_web-4.3.4/
file_picker=/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.1.2/
file_selector_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/
file_selector_macos=/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3/
file_selector_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/
firebase_core=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.8.0/
firebase_core_web=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.18.1/
firebase_messaging=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-15.1.5/
firebase_messaging_web=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_web-3.9.4/
flutter_plugin_android_lifecycle=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.20/
google_sign_in=/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in-6.2.1/
google_sign_in_android=/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_android-6.1.30/
google_sign_in_ios=/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_ios-5.7.7/
google_sign_in_web=/Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_web-0.12.4+2/
gtk=/Users/<USER>/.pub-cache/hosted/pub.dev/gtk-2.1.0/
image_picker=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker-1.1.2/
image_picker_android=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+13/
image_picker_for_web=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.5/
image_picker_ios=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12/
image_picker_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+1/
image_picker_macos=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+1/
image_picker_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/
path_provider=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.4/
path_provider_android=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.10/
path_provider_foundation=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.0/
path_provider_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/
path_provider_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/
shared_preferences=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.3.2/
shared_preferences_android=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.3.2/
shared_preferences_foundation=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.2/
shared_preferences_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/
shared_preferences_web=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.2/
shared_preferences_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/
url_launcher=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.0/
url_launcher_android=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.10/
url_launcher_ios=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.1/
url_launcher_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.0/
url_launcher_macos=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.1/
url_launcher_web=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.3.3/
url_launcher_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.2/
video_player=/Users/<USER>/.pub-cache/hosted/pub.dev/video_player-2.9.2/
video_player_android=/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_android-2.7.13/
video_player_avfoundation=/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.2/
video_player_web=/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_web-2.3.2/
