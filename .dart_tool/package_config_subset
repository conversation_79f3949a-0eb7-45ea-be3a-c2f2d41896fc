_flutterfire_internals
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/_flutterfire_internals-1.3.46/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/_flutterfire_internals-1.3.46/lib/
app_links
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/app_links-6.3.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/app_links-6.3.2/lib/
app_links_linux
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/app_links_linux-1.0.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/app_links_linux-1.0.3/lib/
app_links_platform_interface
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/app_links_platform_interface-2.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/app_links_platform_interface-2.0.2/lib/
app_links_web
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/app_links_web-1.0.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/app_links_web-1.0.4/lib/
archive
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/lib/
args
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/args-2.7.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/args-2.7.0/lib/
async
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/
auto_size_text
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/auto_size_text-3.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/auto_size_text-3.0.0/lib/
boolean_selector
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/lib/
characters
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/
checked_yaml
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.3/lib/
cli_util
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cli_util-0.4.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cli_util-0.4.2/lib/
clock
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/
cloud_firestore
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-5.5.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-5.5.0/lib/
cloud_firestore_platform_interface
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.5.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.5.0/lib/
cloud_firestore_web
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_web-4.3.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_web-4.3.4/lib/
collection
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/
cross_file
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/lib/
crypto
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/
csslib
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/csslib-0.17.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/csslib-0.17.3/lib/
cupertino_icons
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/lib/
data_table_2
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/data_table_2-2.5.10/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/data_table_2-2.5.10/lib/
emoji_flag_converter
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/emoji_flag_converter-1.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/emoji_flag_converter-1.1.0/lib/
fake_async
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.2/lib/
ffi
3.7
file:///Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/
file
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/
file_picker
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.1.2/lib/
file_selector_linux
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/lib/
file_selector_macos
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3/lib/
file_selector_platform_interface
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/
file_selector_windows
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/lib/
firebase_core
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.8.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.8.0/lib/
firebase_core_platform_interface
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.3.0/lib/
firebase_core_web
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.18.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.18.1/lib/
firebase_messaging
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-15.1.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-15.1.5/lib/
firebase_messaging_platform_interface
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_platform_interface-4.5.48/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_platform_interface-4.5.48/lib/
firebase_messaging_web
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_web-3.9.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_web-3.9.4/lib/
fixnum
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/
floating_bottom_navigation_bar
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/floating_bottom_navigation_bar-1.5.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/floating_bottom_navigation_bar-1.5.2/lib/
flutter_animate
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_animate-4.5.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_animate-4.5.0/lib/
flutter_launcher_icons
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_launcher_icons-0.13.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_launcher_icons-0.13.1/lib/
flutter_lints
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-4.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-4.0.0/lib/
flutter_plugin_android_lifecycle
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.20/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.20/lib/
flutter_shaders
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_shaders-0.1.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_shaders-0.1.3/lib/
font_awesome_flutter
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/font_awesome_flutter-10.7.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/font_awesome_flutter-10.7.0/lib/
from_css_color
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/from_css_color-2.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/from_css_color-2.0.0/lib/
functions_client
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/functions_client-2.3.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/functions_client-2.3.2/lib/
go_router
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/go_router-12.1.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/go_router-12.1.3/lib/
google_fonts
2.14
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.1.0/lib/
google_identity_services_web
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_identity_services_web-0.3.3+1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_identity_services_web-0.3.3+1/lib/
google_sign_in
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in-6.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in-6.2.1/lib/
google_sign_in_android
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_android-6.1.30/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_android-6.1.30/lib/
google_sign_in_ios
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_ios-5.7.7/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_ios-5.7.7/lib/
google_sign_in_platform_interface
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_platform_interface-2.4.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_platform_interface-2.4.5/lib/
google_sign_in_web
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_web-0.12.4+2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_web-0.12.4+2/lib/
gotrue
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/gotrue-2.8.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/gotrue-2.8.4/lib/
gtk
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/gtk-2.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/gtk-2.1.0/lib/
html
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.4/lib/
http
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http-1.2.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http-1.2.2/lib/
http_parser
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/
image
2.15
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image-4.2.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image-4.2.0/lib/
image_picker
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker-1.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker-1.1.2/lib/
image_picker_android
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+13/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+13/lib/
image_picker_for_web
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.5/lib/
image_picker_ios
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12/lib/
image_picker_linux
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+1/lib/
image_picker_macos
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+1/lib/
image_picker_platform_interface
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.0/lib/
image_picker_windows
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/lib/
intl
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/
iregexp
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/iregexp-0.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/iregexp-0.1.2/lib/
json_annotation
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib/
json_path
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/json_path-0.7.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/json_path-0.7.2/lib/
jwt_decode
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/jwt_decode-0.3.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/jwt_decode-0.3.1/lib/
leak_tracker
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.8/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.8/lib/
leak_tracker_flutter_testing
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/lib/
leak_tracker_testing
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/lib/
lints
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/lints-4.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/lints-4.0.0/lib/
logging
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/lib/
matcher
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17/lib/
material_color_utilities
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/
maybe_just_nothing
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/maybe_just_nothing-0.5.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/maybe_just_nothing-0.5.3/lib/
meta
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/lib/
mime
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/mime-1.0.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/mime-1.0.6/lib/
mime_type
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/mime_type-1.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/mime_type-1.0.0/lib/
nested
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0/lib/
page_transition
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/page_transition-2.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/page_transition-2.1.0/lib/
path
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/
path_provider
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.4/lib/
path_provider_android
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.10/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.10/lib/
path_provider_foundation
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.0/lib/
path_provider_linux
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/
path_provider_platform_interface
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/
path_provider_windows
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/
petitparser
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/
photo_view
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/photo_view-0.15.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/photo_view-0.15.0/lib/
platform
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/
plugin_platform_interface
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/lib/
postgrest
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/postgrest-2.1.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/postgrest-2.1.4/lib/
provider
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.2/lib/
realtime_client
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/realtime_client-2.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/realtime_client-2.2.1/lib/
retry
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/retry-3.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/retry-3.1.2/lib/
rfc_6901
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/rfc_6901-0.2.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/rfc_6901-0.2.0/lib/
rxdart
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.27.7/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.27.7/lib/
shared_preferences
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.3.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.3.2/lib/
shared_preferences_android
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.3.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.3.2/lib/
shared_preferences_foundation
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.2/lib/
shared_preferences_linux
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/lib/
shared_preferences_platform_interface
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/
shared_preferences_web
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.2/lib/
shared_preferences_windows
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/lib/
simple_gesture_detector
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/simple_gesture_detector-0.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/simple_gesture_detector-0.2.1/lib/
source_span
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/
sprintf
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/
stack_trace
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/
storage_client
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/storage_client-2.0.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/storage_client-2.0.3/lib/
stream_channel
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/lib/
string_scanner
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/
supabase
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/supabase-2.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/supabase-2.3.0/lib/
supabase_flutter
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/supabase_flutter-2.6.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/supabase_flutter-2.6.0/lib/
table_calendar
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/table_calendar-3.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/table_calendar-3.1.1/lib/
term_glyph
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/
test_api
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4/lib/
timeago
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.6.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.6.1/lib/
typed_data
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/
url_launcher
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.0/lib/
url_launcher_android
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.10/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.10/lib/
url_launcher_ios
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.1/lib/
url_launcher_linux
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.0/lib/
url_launcher_macos
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.1/lib/
url_launcher_platform_interface
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/
url_launcher_web
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.3.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.3.3/lib/
url_launcher_windows
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.2/lib/
uuid
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/
vector_math
2.14
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/
video_player
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player-2.9.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player-2.9.2/lib/
video_player_android
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player_android-2.7.13/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player_android-2.7.13/lib/
video_player_avfoundation
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.2/lib/
video_player_platform_interface
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player_platform_interface-6.2.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player_platform_interface-6.2.3/lib/
video_player_web
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player_web-2.3.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player_web-2.3.2/lib/
vm_service
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-14.3.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-14.3.1/lib/
web
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/lib/
web_socket
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web_socket-1.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web_socket-1.0.1/lib/
web_socket_channel
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-3.0.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-3.0.3/lib/
win32
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.5.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.5.1/lib/
xdg_directories
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/lib/
xml
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/
yaml
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.3/lib/
yet_another_json_isolate
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/yet_another_json_isolate-2.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/yet_another_json_isolate-2.0.2/lib/
sky_engine
3.7
file:///Users/<USER>/Developer/flutter/bin/cache/pkg/sky_engine/
file:///Users/<USER>/Developer/flutter/bin/cache/pkg/sky_engine/lib/
flutter
3.7
file:///Users/<USER>/Developer/flutter/packages/flutter/
file:///Users/<USER>/Developer/flutter/packages/flutter/lib/
flutter_localizations
3.7
file:///Users/<USER>/Developer/flutter/packages/flutter_localizations/
file:///Users/<USER>/Developer/flutter/packages/flutter_localizations/lib/
flutter_test
3.7
file:///Users/<USER>/Developer/flutter/packages/flutter_test/
file:///Users/<USER>/Developer/flutter/packages/flutter_test/lib/
flutter_web_plugins
3.7
file:///Users/<USER>/Developer/flutter/packages/flutter_web_plugins/
file:///Users/<USER>/Developer/flutter/packages/flutter_web_plugins/lib/
al_khabeer
3.0
file:///Users/<USER>/StudioProjects/test/
file:///Users/<USER>/StudioProjects/test/lib/
2
