{"configVersion": 2, "packages": [{"name": "_flutterfire_internals", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/_flutterfire_internals-1.3.46", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "app_links", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/app_links-6.3.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "app_links_linux", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/app_links_linux-1.0.3", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "app_links_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/app_links_platform_interface-2.0.2", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "app_links_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/app_links_web-1.0.4", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "archive", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "args", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/args-2.7.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "async", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "auto_size_text", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/auto_size_text-3.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "boolean_selector", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "characters", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "checked_yaml", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.3", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "cli_util", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cli_util-0.4.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "clock", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "cloud_firestore", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-5.5.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "cloud_firestore_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_platform_interface-6.5.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "cloud_firestore_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_web-4.3.4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "collection", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "cross_file", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "crypto", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "csslib", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/csslib-0.17.3", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "cupertino_icons", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "data_table_2", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/data_table_2-2.5.10", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "emoji_flag_converter", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/emoji_flag_converter-1.1.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "fake_async", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "ffi", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "file", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "file_picker", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "file_selector_linux", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "file_selector_macos", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "file_selector_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "file_selector_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "firebase_core", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.8.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_core_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.3.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_core_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.18.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "firebase_messaging", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-15.1.5", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_messaging_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_platform_interface-4.5.48", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_messaging_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_web-3.9.4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "fixnum", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "floating_bottom_navigation_bar", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/floating_bottom_navigation_bar-1.5.2", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter", "rootUri": "file:///Users/<USER>/Developer/flutter/packages/flutter", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_animate", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_animate-4.5.0", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_launcher_icons", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_launcher_icons-0.13.1", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "flutter_lints", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-4.0.0", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "flutter_localizations", "rootUri": "file:///Users/<USER>/Developer/flutter/packages/flutter_localizations", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_plugin_android_lifecycle", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.20", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "flutter_shaders", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_shaders-0.1.3", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "flutter_test", "rootUri": "file:///Users/<USER>/Developer/flutter/packages/flutter_test", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_web_plugins", "rootUri": "file:///Users/<USER>/Developer/flutter/packages/flutter_web_plugins", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "font_awesome_flutter", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/font_awesome_flutter-10.7.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "from_css_color", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/from_css_color-2.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "functions_client", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/functions_client-2.3.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "go_router", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/go_router-12.1.3", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "google_fonts", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.1.0", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "google_identity_services_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_identity_services_web-0.3.3+1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "google_sign_in", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in-6.2.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "google_sign_in_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_android-6.1.30", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "google_sign_in_ios", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_ios-5.7.7", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "google_sign_in_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_platform_interface-2.4.5", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "google_sign_in_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_web-0.12.4+2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "gotrue", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/gotrue-2.8.4", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "gtk", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/gtk-2.1.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "html", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.4", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "http", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/http-1.2.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "http_parser", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image-4.2.0", "packageUri": "lib/", "languageVersion": "2.15"}, {"name": "image_picker", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker-1.1.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "image_picker_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+13", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "image_picker_for_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.5", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "image_picker_ios", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "image_picker_linux", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "image_picker_macos", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "image_picker_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "image_picker_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "intl", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "iregexp", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/iregexp-0.1.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "json_annotation", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "json_path", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/json_path-0.7.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "jwt_decode", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/jwt_decode-0.3.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "leak_tracker", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.8", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_flutter_testing", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_testing", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "lints", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/lints-4.0.0", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "logging", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "matcher", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "material_color_utilities", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "maybe_just_nothing", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/maybe_just_nothing-0.5.3", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "meta", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "mime", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/mime-1.0.6", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "mime_type", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/mime_type-1.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "nested", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "page_transition", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/page_transition-2.1.0", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "path", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "path_provider", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.4", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "path_provider_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.10", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "path_provider_foundation", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "path_provider_linux", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "path_provider_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "path_provider_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "petitparser", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "photo_view", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/photo_view-0.15.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "platform", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "plugin_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "postgrest", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/postgrest-2.1.4", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "provider", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.2", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "realtime_client", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/realtime_client-2.2.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "retry", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/retry-3.1.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "rfc_6901", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/rfc_6901-0.2.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "rxdart", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.27.7", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "shared_preferences", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.3.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "shared_preferences_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.3.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "shared_preferences_foundation", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "shared_preferences_linux", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "shared_preferences_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "shared_preferences_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "shared_preferences_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "simple_gesture_detector", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/simple_gesture_detector-0.2.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "sky_engine", "rootUri": "file:///Users/<USER>/Developer/flutter/bin/cache/pkg/sky_engine", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "source_span", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "sprintf", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "stack_trace", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "storage_client", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/storage_client-2.0.3", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "stream_channel", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "string_scanner", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "supabase", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/supabase-2.3.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "supabase_flutter", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/supabase_flutter-2.6.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "table_calendar", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/table_calendar-3.1.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "term_glyph", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "test_api", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "timeago", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.6.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "typed_data", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "url_launcher", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "url_launcher_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.10", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "url_launcher_ios", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "url_launcher_linux", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "url_launcher_macos", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "url_launcher_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "url_launcher_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.3.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "url_launcher_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.2", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "uuid", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "vector_math", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "video_player", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player-2.9.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "video_player_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player_android-2.7.13", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "video_player_avfoundation", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "video_player_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player_platform_interface-6.2.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "video_player_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/video_player_web-2.3.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "vm_service", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-14.3.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "web_socket", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/web_socket-1.0.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "web_socket_channel", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-3.0.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "win32", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.5.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "xdg_directories", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "xml", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "yaml", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "yet_another_json_isolate", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/yet_another_json_isolate-2.0.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rootUri": "../", "packageUri": "lib/", "languageVersion": "3.0"}], "generated": "2025-06-30T11:07:04.020144Z", "generator": "pub", "generatorVersion": "3.7.2", "flutterRoot": "file:///Users/<USER>/Developer/flutter", "flutterVersion": "3.29.2", "pubCache": "file:///Users/<USER>/.pub-cache"}