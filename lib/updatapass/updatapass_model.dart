import '/flutter_flow/flutter_flow_util.dart';
import '/index.dart';
import 'updatapass_widget.dart' show UpdatapassWidget;
import 'package:flutter/material.dart';

class UpdatapassModel extends FlutterFlowModel<UpdatapassWidget> {
  ///  State fields for stateful widgets in this page.

  // State field(s) for emailAddress widget.
  FocusNode? emailAddressFocusNode;
  TextEditingController? emailAddressTextController;
  late bool emailAddressVisibility;
  String? Function(BuildContext, String?)? emailAddressTextControllerValidator;
  // State field(s) for password_CreateConfirm widget.
  FocusNode? passwordCreateConfirmFocusNode;
  TextEditingController? passwordCreateConfirmTextController;
  late bool passwordCreateConfirmVisibility;
  String? Function(BuildContext, String?)?
      passwordCreateConfirmTextControllerValidator;

  @override
  void initState(BuildContext context) {
    emailAddressVisibility = false;
    passwordCreateConfirmVisibility = false;
  }

  @override
  void dispose() {
    emailAddressFocusNode?.dispose();
    emailAddressTextController?.dispose();

    passwordCreateConfirmFocusNode?.dispose();
    passwordCreateConfirmTextController?.dispose();
  }
}
