import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class FFAppState extends ChangeNotifier {
  static FFAppState _instance = FFAppState._internal();

  factory FFAppState() {
    return _instance;
  }

  FFAppState._internal();

  static void reset() {
    _instance = FFAppState._internal();
  }

  Future initializePersistedState() async {
    prefs = await SharedPreferences.getInstance();
    _safeInit(() {
      _selectedLanguageCode =
          prefs.getString('ff_selectedLanguageCode') ?? _selectedLanguageCode;
    });
    _safeInit(() {
      _userRole = prefs.getString('ff_userRole') ?? _userRole;
    });
  }

  void update(VoidCallback callback) {
    callback();
    notifyListeners();
  }

  late SharedPreferences prefs;

  String _categoryy = '';
  String get categoryy => _categoryy;
  set categoryy(String value) {
    _categoryy = value;
  }

  String _fcmToken = 'noToken';
  String get fcmToken => _fcmToken;
  set fcmToken(String value) {
    _fcmToken = value;
  }

  String _jwtToken = '';
  String get jwtToken => _jwtToken;
  set jwtToken(String value) {
    _jwtToken = value;
  }

  String _selectedLanguageCode = '';
  String get selectedLanguageCode => _selectedLanguageCode;
  set selectedLanguageCode(String value) {
    _selectedLanguageCode = value;
    prefs.setString('ff_selectedLanguageCode', value);
  }

  DateTime? _currentUserCreationDate;
  DateTime? get currentUserCreationDate => _currentUserCreationDate;
  set currentUserCreationDate(DateTime? value) {
    _currentUserCreationDate = value;
  }

  String _userRole = '';
  String get userRole => _userRole;
  set userRole(String value) {
    _userRole = value;
    prefs.setString('ff_userRole', value);
  }
}

void _safeInit(Function() initializeField) {
  try {
    initializeField();
  } catch (_) {}
}
