import '/backend/supabase/supabase.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/index.dart';
import 'profile2_widget.dart' show Profile2Widget;
import 'package:flutter/material.dart';

class Profile2Model extends FlutterFlowModel<Profile2Widget> {
  ///  State fields for stateful widgets in this page.

  // Stores action output result for [Backend Call - Delete Row(s)] action in Button widget.
  List<UsersRow>? deleteAccount;

  @override
  void initState(BuildContext context) {}

  @override
  void dispose() {}
}
