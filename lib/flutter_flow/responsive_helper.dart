import 'package:flutter/material.dart';

/// مساعد للتعامل مع الاستجابة للشاشات المختلفة
class ResponsiveHelper {
  
  /// الحصول على عرض الشاشة
  static double getScreenWidth(BuildContext context) {
    return MediaQuery.of(context).size.width;
  }

  /// الحصول على ارتفاع الشاشة
  static double getScreenHeight(BuildContext context) {
    return MediaQuery.of(context).size.height;
  }

  /// التحقق من نوع الجهاز
  static bool isMobile(BuildContext context) {
    return getScreenWidth(context) < 768;
  }

  static bool isTablet(BuildContext context) {
    final width = getScreenWidth(context);
    return width >= 768 && width < 1024;
  }

  static bool isDesktop(BuildContext context) {
    return getScreenWidth(context) >= 1024;
  }

  /// الحصول على عدد الأعمدة المناسب للشبكة
  static int getGridCrossAxisCount(BuildContext context) {
    if (isMobile(context)) {
      return 2;
    } else if (isTablet(context)) {
      return 3;
    } else {
      return 4;
    }
  }

  /// الحصول على المسافة المناسبة للحواف
  static EdgeInsets getScreenPadding(BuildContext context) {
    if (isMobile(context)) {
      return const EdgeInsets.all(16.0);
    } else if (isTablet(context)) {
      return const EdgeInsets.all(24.0);
    } else {
      return const EdgeInsets.all(32.0);
    }
  }

  /// الحصول على حجم الخط المناسب
  static double getResponsiveFontSize(BuildContext context, double baseFontSize) {
    final screenWidth = getScreenWidth(context);
    
    if (screenWidth < 360) {
      return baseFontSize * 0.9;
    } else if (screenWidth > 768) {
      return baseFontSize * 1.1;
    }
    
    return baseFontSize;
  }

  /// الحصول على ارتفاع مناسب للبطاقات
  static double getCardHeight(BuildContext context) {
    if (isMobile(context)) {
      return 200.0;
    } else if (isTablet(context)) {
      return 250.0;
    } else {
      return 300.0;
    }
  }

  /// الحصول على عرض مناسب للحاويات
  static double getContainerWidth(BuildContext context, {double maxWidth = 600.0}) {
    final screenWidth = getScreenWidth(context);
    
    if (screenWidth > maxWidth) {
      return maxWidth;
    }
    
    return screenWidth * 0.9;
  }

  /// الحصول على نسبة العرض إلى الارتفاع للبطاقات
  static double getCardAspectRatio(BuildContext context) {
    if (isMobile(context)) {
      return 0.8;
    } else if (isTablet(context)) {
      return 0.9;
    } else {
      return 1.0;
    }
  }

  /// الحصول على المسافة بين العناصر
  static double getSpacing(BuildContext context, {double baseSpacing = 16.0}) {
    if (isMobile(context)) {
      return baseSpacing;
    } else if (isTablet(context)) {
      return baseSpacing * 1.25;
    } else {
      return baseSpacing * 1.5;
    }
  }

  /// الحصول على نصف قطر الحدود المناسب
  static double getBorderRadius(BuildContext context, {double baseBorderRadius = 12.0}) {
    if (isMobile(context)) {
      return baseBorderRadius;
    } else if (isTablet(context)) {
      return baseBorderRadius * 1.2;
    } else {
      return baseBorderRadius * 1.4;
    }
  }

  /// الحصول على حجم الأيقونة المناسب
  static double getIconSize(BuildContext context, {double baseIconSize = 24.0}) {
    if (isMobile(context)) {
      return baseIconSize;
    } else if (isTablet(context)) {
      return baseIconSize * 1.2;
    } else {
      return baseIconSize * 1.4;
    }
  }

  /// Widget مخصص للتخطيط المتجاوب
  static Widget responsiveBuilder({
    required BuildContext context,
    required Widget mobile,
    Widget? tablet,
    Widget? desktop,
  }) {
    if (isDesktop(context) && desktop != null) {
      return desktop;
    } else if (isTablet(context) && tablet != null) {
      return tablet;
    } else {
      return mobile;
    }
  }

  /// الحصول على عدد العناصر في الصف للقوائم
  static int getListItemsPerRow(BuildContext context) {
    if (isMobile(context)) {
      return 1;
    } else if (isTablet(context)) {
      return 2;
    } else {
      return 3;
    }
  }

  /// الحصول على ارتفاع AppBar المناسب
  static double getAppBarHeight(BuildContext context) {
    if (isMobile(context)) {
      return kToolbarHeight;
    } else {
      return kToolbarHeight * 1.2;
    }
  }

  /// الحصول على حجم FloatingActionButton المناسب
  static double getFABSize(BuildContext context) {
    if (isMobile(context)) {
      return 56.0;
    } else if (isTablet(context)) {
      return 64.0;
    } else {
      return 72.0;
    }
  }

  /// الحصول على المسافة السفلية للـ FloatingActionButton
  static double getFABBottomMargin(BuildContext context) {
    if (isMobile(context)) {
      return 85.0;
    } else if (isTablet(context)) {
      return 100.0;
    } else {
      return 120.0;
    }
  }

  /// الحصول على عرض الـ Drawer
  static double getDrawerWidth(BuildContext context) {
    final screenWidth = getScreenWidth(context);
    
    if (isMobile(context)) {
      return screenWidth * 0.8;
    } else if (isTablet(context)) {
      return 320.0;
    } else {
      return 360.0;
    }
  }

  /// الحصول على ارتفاع الـ BottomSheet
  static double getBottomSheetHeight(BuildContext context) {
    final screenHeight = getScreenHeight(context);
    
    if (isMobile(context)) {
      return screenHeight * 0.9;
    } else if (isTablet(context)) {
      return screenHeight * 0.8;
    } else {
      return screenHeight * 0.7;
    }
  }

  /// الحصول على عرض الحد الأقصى للمحتوى
  static double getMaxContentWidth(BuildContext context) {
    if (isMobile(context)) {
      return double.infinity;
    } else if (isTablet(context)) {
      return 768.0;
    } else {
      return 1200.0;
    }
  }

  /// Widget للمحتوى المتمركز مع عرض أقصى
  static Widget centeredContent({
    required BuildContext context,
    required Widget child,
    double? maxWidth,
  }) {
    final effectiveMaxWidth = maxWidth ?? getMaxContentWidth(context);
    
    return Center(
      child: ConstrainedBox(
        constraints: BoxConstraints(maxWidth: effectiveMaxWidth),
        child: child,
      ),
    );
  }

  /// الحصول على التخطيط المناسب للشبكة
  static SliverGridDelegate getGridDelegate(BuildContext context) {
    return SliverGridDelegateWithFixedCrossAxisCount(
      crossAxisCount: getGridCrossAxisCount(context),
      crossAxisSpacing: getSpacing(context),
      mainAxisSpacing: getSpacing(context),
      childAspectRatio: getCardAspectRatio(context),
    );
  }

  /// الحصول على التخطيط المناسب للشبكة مع عرض أقصى
  static SliverGridDelegate getGridDelegateWithMaxWidth(
    BuildContext context, {
    double maxCrossAxisExtent = 300.0,
  }) {
    return SliverGridDelegateWithMaxCrossAxisExtent(
      maxCrossAxisExtent: maxCrossAxisExtent,
      crossAxisSpacing: getSpacing(context),
      mainAxisSpacing: getSpacing(context),
      childAspectRatio: getCardAspectRatio(context),
    );
  }
}

/// Extension لتسهيل الوصول للوظائف المتجاوبة
extension ResponsiveExtension on BuildContext {
  bool get isMobile => ResponsiveHelper.isMobile(this);
  bool get isTablet => ResponsiveHelper.isTablet(this);
  bool get isDesktop => ResponsiveHelper.isDesktop(this);
  
  double get screenWidth => ResponsiveHelper.getScreenWidth(this);
  double get screenHeight => ResponsiveHelper.getScreenHeight(this);
  
  EdgeInsets get screenPadding => ResponsiveHelper.getScreenPadding(this);
  double get spacing => ResponsiveHelper.getSpacing(this);
  double get borderRadius => ResponsiveHelper.getBorderRadius(this);
}
