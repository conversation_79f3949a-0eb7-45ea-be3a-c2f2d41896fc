{"buildFiles": ["/Users/<USER>/Developer/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/StudioProjects/test/android/app/.cxx/Debug/2d2ml5hj/arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/StudioProjects/test/android/app/.cxx/Debug/2d2ml5hj/arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}