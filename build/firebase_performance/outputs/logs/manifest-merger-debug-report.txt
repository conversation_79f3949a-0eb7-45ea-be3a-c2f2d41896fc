-- Merging decision tree log ---
manifest
ADDED from /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_performance-0.10.0+10/android/src/main/AndroidManifest.xml:1:1-11:12
INJECTED from /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_performance-0.10.0+10/android/src/main/AndroidManifest.xml:1:1-11:12
	package
		ADDED from /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_performance-0.10.0+10/android/src/main/AndroidManifest.xml:2:3-52
		INJECTED from /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_performance-0.10.0+10/android/src/main/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_performance-0.10.0+10/android/src/main/AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_performance-0.10.0+10/android/src/main/AndroidManifest.xml:4:3-65
	android:name
		ADDED from /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_performance-0.10.0+10/android/src/main/AndroidManifest.xml:4:20-62
application
ADDED from /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_performance-0.10.0+10/android/src/main/AndroidManifest.xml:5:3-10:17
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_performance-0.10.0+10/android/src/main/AndroidManifest.xml:6:5-9:15
	android:name
		ADDED from /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_performance-0.10.0+10/android/src/main/AndroidManifest.xml:6:14-85
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.performance.FlutterFirebaseAppRegistrar
ADDED from /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_performance-0.10.0+10/android/src/main/AndroidManifest.xml:7:7-8:86
	android:value
		ADDED from /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_performance-0.10.0+10/android/src/main/AndroidManifest.xml:8:18-83
	android:name
		ADDED from /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_performance-0.10.0+10/android/src/main/AndroidManifest.xml:7:18-131
uses-sdk
INJECTED from /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_performance-0.10.0+10/android/src/main/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_performance-0.10.0+10/android/src/main/AndroidManifest.xml
INJECTED from /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_performance-0.10.0+10/android/src/main/AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_performance-0.10.0+10/android/src/main/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_performance-0.10.0+10/android/src/main/AndroidManifest.xml
