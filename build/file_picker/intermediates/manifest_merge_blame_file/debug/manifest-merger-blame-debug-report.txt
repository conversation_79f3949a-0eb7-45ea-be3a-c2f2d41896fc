1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.mr.flutter.plugin.filepicker" >
4
5    <uses-sdk android:minSdkVersion="21" />
6
7    <uses-permission
7-->/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.1.2/android/src/main/AndroidManifest.xml:3:5-106
8        android:name="android.permission.READ_EXTERNAL_STORAGE"
8-->/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.1.2/android/src/main/AndroidManifest.xml:3:22-77
9        android:maxSdkVersion="32" />
9-->/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.1.2/android/src/main/AndroidManifest.xml:3:78-104
10
11    <queries>
11-->/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.1.2/android/src/main/AndroidManifest.xml:4:5-9:15
12        <intent>
12-->/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.1.2/android/src/main/AndroidManifest.xml:5:6-8:14
13            <action android:name="android.intent.action.GET_CONTENT" />
13-->/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.1.2/android/src/main/AndroidManifest.xml:6:9-68
13-->/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.1.2/android/src/main/AndroidManifest.xml:6:17-65
14
15            <data android:mimeType="*/*" />
15-->/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.1.2/android/src/main/AndroidManifest.xml:7:9-39
15-->/Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.1.2/android/src/main/AndroidManifest.xml:7:15-37
16        </intent>
17    </queries>
18
19</manifest>
