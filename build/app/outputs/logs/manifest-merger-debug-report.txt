-- Merging decision tree log ---
manifest
ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:1:1-65:12
MERGED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:1:1-65:12
INJECTED from /Users/<USER>/StudioProjects/test/android/app/src/debug/AndroidManifest.xml:1:1-7:12
INJECTED from /Users/<USER>/StudioProjects/test/android/app/src/debug/AndroidManifest.xml:1:1-7:12
INJECTED from /Users/<USER>/StudioProjects/test/android/app/src/debug/AndroidManifest.xml:1:1-7:12
MERGED from [:app_links] /Users/<USER>/StudioProjects/test/build/app_links/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:cloud_firestore] /Users/<USER>/StudioProjects/test/build/cloud_firestore/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-15:12
MERGED from [:file_picker] /Users/<USER>/StudioProjects/test/build/file_picker/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-19:12
MERGED from [:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-48:12
MERGED from [:firebase_performance] /Users/<USER>/StudioProjects/test/build/firebase_performance/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-17:12
MERGED from [:firebase_core] /Users/<USER>/StudioProjects/test/build/firebase_core/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-15:12
MERGED from [:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-34:12
MERGED from [:flutter_plugin_android_lifecycle] /Users/<USER>/StudioProjects/test/build/flutter_plugin_android_lifecycle/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:google_sign_in_android] /Users/<USER>/StudioProjects/test/build/google_sign_in_android/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-9:12
MERGED from [:path_provider_android] /Users/<USER>/StudioProjects/test/build/path_provider_android/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:shared_preferences_android] /Users/<USER>/StudioProjects/test/build/shared_preferences_android/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:sign_in_with_apple] /Users/<USER>/StudioProjects/test/build/sign_in_with_apple/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:sqflite] /Users/<USER>/StudioProjects/test/build/sqflite/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:url_launcher_android] /Users/<USER>/StudioProjects/test/build/url_launcher_android/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-14:12
MERGED from [:video_player_android] /Users/<USER>/StudioProjects/test/build/video_player_android/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [:webview_flutter_android] /Users/<USER>/StudioProjects/test/build/webview_flutter_android/intermediates/merged_manifest/debug/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-perf:21.0.2] /Users/<USER>/.gradle/caches/transforms-3/b863a0e80e78acee3b999a288117c346/transformed/jetified-firebase-perf-21.0.2/AndroidManifest.xml:3:1-23:12
MERGED from [com.google.firebase:firebase-sessions:2.0.0] /Users/<USER>/.gradle/caches/transforms-3/7243a85cae0957d4f2c115b4e3710992/transformed/jetified-firebase-sessions-2.0.0/AndroidManifest.xml:15:1-35:12
MERGED from [com.google.firebase:firebase-firestore:25.1.1] /Users/<USER>/.gradle/caches/transforms-3/530d6bd4293568127cdd25ad75f71303/transformed/jetified-firebase-firestore-25.1.1/AndroidManifest.xml:2:1-26:12
MERGED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:17:1-66:12
MERGED from [com.google.firebase:firebase-config:22.0.1] /Users/<USER>/.gradle/caches/transforms-3/be0c224c4da8bf7baeafb21de3f01ca4/transformed/jetified-firebase-config-22.0.1/AndroidManifest.xml:17:1-38:12
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/1a290a3f05b797b7236a51bcc1d7f52a/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/172cbceb397b342c9cf843eb432fc541/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-config-interop:16.0.1] /Users/<USER>/.gradle/caches/transforms-3/c145f584d00a811dbc467b0f60d74396/transformed/jetified-firebase-config-interop-16.0.1/AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/transforms-3/43c44d29c9ea2875571e40397afa4dbc/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:15:1-31:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/96ffa47d0dd9b7416c80de5663a8865a/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:15:1-37:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.1] /Users/<USER>/.gradle/caches/transforms-3/2adf4a250f618d3d504407f438449213/transformed/jetified-firebase-encoders-json-18.0.1/AndroidManifest.xml:15:1-22:12
MERGED from [androidx.datastore:datastore-preferences:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/b720a443358c871b0022dcf4745b1542/transformed/jetified-datastore-preferences-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.datastore:datastore:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/8913d7690b261cf7123c3be7247fc1fb/transformed/jetified-datastore-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/87fac691493df4f79988be19c1e693b2/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:17:1-40:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] /Users/<USER>/.gradle/caches/transforms-3/0e835b4afdda5af2322da9e33afcefa3/transformed/jetified-play-services-auth-api-phone-18.0.2/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] /Users/<USER>/.gradle/caches/transforms-3/7b58ec1d7a2add5a173714af0a87c160/transformed/jetified-play-services-auth-base-18.0.10/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-fido:20.0.1] /Users/<USER>/.gradle/caches/transforms-3/c162869218be1eccef38b435236abde4/transformed/jetified-play-services-fido-20.0.1/AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] /Users/<USER>/.gradle/caches/transforms-3/bd16f126880ebca356bc63482a1d9c89/transformed/jetified-firebase-appcheck-interop-17.0.0/AndroidManifest.xml:15:1-25:12
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] /Users/<USER>/.gradle/caches/transforms-3/d5860f6dcea01ec41b8928a37336e6cd/transformed/jetified-firebase-database-collection-18.0.1/AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/transforms-3/44f3b8f078250072ad3e855c1c8d37b1/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:16:1-24:12
MERGED from [androidx.appcompat:appcompat:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/953d05e3fa95b7ff82a1fd5ab5bbc38e/transformed/appcompat-1.2.0/AndroidManifest.xml:17:1-24:12
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/357784e7a16ccd95c68cfb70e35fa59d/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:15:1-41:12
MERGED from [com.google.firebase:firebase-auth-interop:19.0.2] /Users/<USER>/.gradle/caches/transforms-3/1508eb55e8f880ed69749b98c5d0efc6/transformed/jetified-firebase-auth-interop-19.0.2/AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] /Users/<USER>/.gradle/caches/transforms-3/8c7ceadf0682a796ab1dc6c947dad809/transformed/jetified-firebase-iid-interop-17.1.0/AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-abt:21.1.1] /Users/<USER>/.gradle/caches/transforms-3/fed47fe0e911f93eba0838da176bd2db/transformed/jetified-firebase-abt-21.1.1/AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/transforms-3/8c4c177b5c22dbb533181b6f38d61b8d/transformed/jetified-firebase-measurement-connector-19.0.0/AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/63a2baf8c135443c6637edfc9d04aed6/transformed/jetified-play-services-cloud-messaging-17.2.0/AndroidManifest.xml:2:1-13:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/transforms-3/ab28426cb8a44bb7ef8565ff9d38f5d9/transformed/jetified-play-services-stats-17.0.2/AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] /Users/<USER>/.gradle/caches/transforms-3/920afd2299c726bbedf590bb865fea5e/transformed/jetified-firebase-installations-interop-17.1.1/AndroidManifest.xml:15:1-19:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/cf023b4654ab7eda9cfdca5c6b56e762/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/d692ab5775189a752d3ebc49b04d52ec/transformed/loader-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/aeb460594c9750ecf4d241b7622c57eb/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/0d724274b46780f68492593c5138c21e/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/98a13afb1037d30c765555e4ae128327/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/6975cc431ec8498ffdf2796b67759ca0/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window:window-java:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/d29f4b4a0b5f264567f654ef8608c1f5/transformed/jetified-window-java-1.2.0/AndroidManifest.xml:17:1-21:12
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/1a8b478347169530bac076d1af4efc75/transformed/jetified-window-1.2.0/AndroidManifest.xml:17:1-31:12
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] /Users/<USER>/.gradle/caches/transforms-3/e0b21fbc1eecde26e0feacc16c1372b1/transformed/jetified-play-services-tasks-18.1.0/AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/transforms-3/6408c3c41b137332f20e3d3bc0428c8c/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.7.1] /Users/<USER>/.gradle/caches/transforms-3/1125d6a8eabbe5b42dadf26115c520ee/transformed/fragment-1.7.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.9.1] /Users/<USER>/.gradle/caches/transforms-3/6fbe174a0d7b8561e64c5b995ae99f9f/transformed/jetified-activity-1.9.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.browser:browser:1.8.0] /Users/<USER>/.gradle/caches/transforms-3/86ee45f5c791d8d00fa99ae2b84aaec9/transformed/browser-1.8.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.media3:media3-extractor:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/8cad0cadf2eff4aeeabb85714040df6f/transformed/jetified-media3-extractor-1.4.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-container:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/c74f2068ed98f20276cbfc6886ee9610/transformed/jetified-media3-container-1.4.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/11f98baa43b386be292468958e7a4230/transformed/jetified-media3-datasource-1.4.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-decoder:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/1d28d14250a5a46382d8ea8d6ef95c81/transformed/jetified-media3-decoder-1.4.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-database:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/2a1536eae5f5020b732ac916ee2f951a/transformed/jetified-media3-database-1.4.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-common:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/53101df6edb2b8fbeb4cd06a735b22ad/transformed/jetified-media3-common-1.4.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/dea0a95a2c925b9a749a72f8dfade75f/transformed/jetified-media3-exoplayer-hls-1.4.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/92a976be9b00372484ead900aeb17dd5/transformed/jetified-media3-exoplayer-dash-1.4.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-rtsp:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/9cceb47dae5cb8dea4613d76b0f6ecd6/transformed/jetified-media3-exoplayer-rtsp-1.4.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/9245854b7846ebddcb972974fbb29333/transformed/jetified-media3-exoplayer-smoothstreaming-1.4.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/62e4a06f332e44aa23e2085894e571ac/transformed/jetified-media3-exoplayer-1.4.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.webkit:webkit:1.11.0] /Users/<USER>/.gradle/caches/transforms-3/5a545179b0d0210524bd88420d5fad40/transformed/webkit-1.11.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/18183d2636cbce3a4f21b0eafd05aafb/transformed/viewpager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/1605e6ec768bd6e9706a2268045ef7db/transformed/jetified-core-ktx-1.13.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/58df69eefc1e2025970934b53128ba84/transformed/drawerlayout-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/2af298b413dea7157a95380e484c9fe8/transformed/customview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/2af213816ce04d40f36d51728a91bf19/transformed/jetified-appcompat-resources-1.2.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/b202e5b322aa44017154942c3e3ecf50/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/4b42d40141582d0bce47d3a0ed91d6d3/transformed/vectordrawable-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/a3de342a3f81e3197d28e19f8e01251f/transformed/core-1.13.1/AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/d8089a24dc295780ff7ab5b60620a9e5/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/transforms-3/c5506762b7ddec4c3f17606aa1c6ab04/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/40fcdb2fe9907dd26d5d7b998f10b9f4/transformed/localbroadcastmanager-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] /Users/<USER>/.gradle/caches/transforms-3/9f4b17e0e9be6f79a754a7bca430ad87/transformed/exifinterface-1.3.7/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/8bc68417b2d3520d1ae45fa0d3b14d45/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/7ec59fd88e2ebab119ff7cd6fa5e9583/transformed/jetified-tracing-1.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/62ee97de108aafd046aef50d76128dfd/transformed/interpolator-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/79b1232db1fbcd0a44e6497dbb9ac93b/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/transforms-3/b4e148e8fdd16144492c40b53ee11bd9/transformed/core-runtime-2.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/bb3745cbc6a48c9f3e1477d3fdacee76/transformed/jetified-firebase-components-18.0.0/AndroidManifest.xml:15:1-20:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/7309f79d0976d2d00ad66906f83b59f3/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:3.1.0] /Users/<USER>/.gradle/caches/transforms-3/d9fa67c2541d7cf24178998779ce82f6/transformed/jetified-transport-api-3.1.0/AndroidManifest.xml:15:1-20:12
MERGED from [androidx.window.extensions.core:core:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/cd42dcb6a1fe8bf7708f7a71548d8de6/transformed/jetified-core-1.0.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/0822bdcc5aeb03272bc4320a44285100/transformed/cursoradapter-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/8bc26783ec4195e6f1c3d720ed8f9925/transformed/documentfile-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/07be7164f23f4ca322e8272a0c413f2c/transformed/print-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [org.microg:safe-parcel:1.7.0] /Users/<USER>/.gradle/caches/transforms-3/2b33262094d94d701b641b5df2c979b7/transformed/jetified-safe-parcel-1.7.0/AndroidManifest.xml:6:1-14:12
MERGED from [io.grpc:grpc-android:1.62.2] /Users/<USER>/.gradle/caches/transforms-3/6d72ee594cdad76bd739f1792ad4c6ba/transformed/jetified-grpc-android-1.62.2/AndroidManifest.xml:2:1-9:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] /Users/<USER>/.gradle/caches/transforms-3/20b2dca2f480923336e8ef7b085e6e58/transformed/jetified-annotation-experimental-1.4.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] /Users/<USER>/.gradle/caches/transforms-3/d8b41ce662ae51121ec0b03c25271f69/transformed/jetified-relinker-1.4.5/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/78188d12c9adb696ed6c96facd320437/transformed/jetified-protolite-well-known-types-18.0.0/AndroidManifest.xml:2:1-11:12
	package
		ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:2:5-28
		INJECTED from /Users/<USER>/StudioProjects/test/android/app/src/debug/AndroidManifest.xml
	android:versionName
		INJECTED from /Users/<USER>/StudioProjects/test/android/app/src/debug/AndroidManifest.xml
	xmlns:tools
		ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:3:5-51
		ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from /Users/<USER>/StudioProjects/test/android/app/src/debug/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:4:5-66
MERGED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:4:5-66
MERGED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:4:5-66
MERGED from [:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-67
MERGED from [:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-67
MERGED from [:firebase_performance] /Users/<USER>/StudioProjects/test/build/firebase_performance/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-67
MERGED from [:firebase_performance] /Users/<USER>/StudioProjects/test/build/firebase_performance/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-67
MERGED from [:google_sign_in_android] /Users/<USER>/StudioProjects/test/build/google_sign_in_android/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-67
MERGED from [:google_sign_in_android] /Users/<USER>/StudioProjects/test/build/google_sign_in_android/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-67
MERGED from [com.google.firebase:firebase-perf:21.0.2] /Users/<USER>/.gradle/caches/transforms-3/b863a0e80e78acee3b999a288117c346/transformed/jetified-firebase-perf-21.0.2/AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-perf:21.0.2] /Users/<USER>/.gradle/caches/transforms-3/b863a0e80e78acee3b999a288117c346/transformed/jetified-firebase-perf-21.0.2/AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-firestore:25.1.1] /Users/<USER>/.gradle/caches/transforms-3/530d6bd4293568127cdd25ad75f71303/transformed/jetified-firebase-firestore-25.1.1/AndroidManifest.xml:11:5-67
MERGED from [com.google.firebase:firebase-firestore:25.1.1] /Users/<USER>/.gradle/caches/transforms-3/530d6bd4293568127cdd25ad75f71303/transformed/jetified-firebase-firestore-25.1.1/AndroidManifest.xml:11:5-67
MERGED from [com.google.firebase:firebase-config:22.0.1] /Users/<USER>/.gradle/caches/transforms-3/be0c224c4da8bf7baeafb21de3f01ca4/transformed/jetified-firebase-config-22.0.1/AndroidManifest.xml:23:5-67
MERGED from [com.google.firebase:firebase-config:22.0.1] /Users/<USER>/.gradle/caches/transforms-3/be0c224c4da8bf7baeafb21de3f01ca4/transformed/jetified-firebase-config-22.0.1/AndroidManifest.xml:23:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/1a290a3f05b797b7236a51bcc1d7f52a/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/1a290a3f05b797b7236a51bcc1d7f52a/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:8:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/96ffa47d0dd9b7416c80de5663a8865a/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/96ffa47d0dd9b7416c80de5663a8865a/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:25:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/63a2baf8c135443c6637edfc9d04aed6/transformed/jetified-play-services-cloud-messaging-17.2.0/AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/63a2baf8c135443c6637edfc9d04aed6/transformed/jetified-play-services-cloud-messaging-17.2.0/AndroidManifest.xml:8:5-67
	android:name
		ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:4:22-64
uses-permission#android.permission.CAMERA
ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:5:5-64
	android:name
		ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:5:22-62
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:6:5-106
MERGED from [:file_picker] /Users/<USER>/StudioProjects/test/build/file_picker/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-9:38
MERGED from [:file_picker] /Users/<USER>/StudioProjects/test/build/file_picker/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-9:38
	android:maxSdkVersion
		ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:6:78-104
	android:name
		ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:6:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:7:5-107
	android:maxSdkVersion
		ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:7:79-105
	android:name
		ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:7:22-78
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:8:5-76
MERGED from [:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:11:5-77
MERGED from [:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:11:5-77
MERGED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:23:5-77
MERGED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:23:5-77
	android:name
		ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:8:22-74
application
ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:9:5-64:19
INJECTED from /Users/<USER>/StudioProjects/test/android/app/src/debug/AndroidManifest.xml
MERGED from [:cloud_firestore] /Users/<USER>/StudioProjects/test/build/cloud_firestore/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-13:19
MERGED from [:cloud_firestore] /Users/<USER>/StudioProjects/test/build/cloud_firestore/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:13:5-46:19
MERGED from [:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:13:5-46:19
MERGED from [:firebase_performance] /Users/<USER>/StudioProjects/test/build/firebase_performance/intermediates/merged_manifest/debug/AndroidManifest.xml:9:5-15:19
MERGED from [:firebase_performance] /Users/<USER>/StudioProjects/test/build/firebase_performance/intermediates/merged_manifest/debug/AndroidManifest.xml:9:5-15:19
MERGED from [:firebase_core] /Users/<USER>/StudioProjects/test/build/firebase_core/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_core] /Users/<USER>/StudioProjects/test/build/firebase_core/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-13:19
MERGED from [:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:8:5-32:19
MERGED from [:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:8:5-32:19
MERGED from [:url_launcher_android] /Users/<USER>/StudioProjects/test/build/url_launcher_android/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-12:19
MERGED from [:url_launcher_android] /Users/<USER>/StudioProjects/test/build/url_launcher_android/intermediates/merged_manifest/debug/AndroidManifest.xml:7:5-12:19
MERGED from [com.google.firebase:firebase-perf:21.0.2] /Users/<USER>/.gradle/caches/transforms-3/b863a0e80e78acee3b999a288117c346/transformed/jetified-firebase-perf-21.0.2/AndroidManifest.xml:10:5-21:19
MERGED from [com.google.firebase:firebase-perf:21.0.2] /Users/<USER>/.gradle/caches/transforms-3/b863a0e80e78acee3b999a288117c346/transformed/jetified-firebase-perf-21.0.2/AndroidManifest.xml:10:5-21:19
MERGED from [com.google.firebase:firebase-sessions:2.0.0] /Users/<USER>/.gradle/caches/transforms-3/7243a85cae0957d4f2c115b4e3710992/transformed/jetified-firebase-sessions-2.0.0/AndroidManifest.xml:21:5-33:19
MERGED from [com.google.firebase:firebase-sessions:2.0.0] /Users/<USER>/.gradle/caches/transforms-3/7243a85cae0957d4f2c115b4e3710992/transformed/jetified-firebase-sessions-2.0.0/AndroidManifest.xml:21:5-33:19
MERGED from [com.google.firebase:firebase-firestore:25.1.1] /Users/<USER>/.gradle/caches/transforms-3/530d6bd4293568127cdd25ad75f71303/transformed/jetified-firebase-firestore-25.1.1/AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-firestore:25.1.1] /Users/<USER>/.gradle/caches/transforms-3/530d6bd4293568127cdd25ad75f71303/transformed/jetified-firebase-firestore-25.1.1/AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-config:22.0.1] /Users/<USER>/.gradle/caches/transforms-3/be0c224c4da8bf7baeafb21de3f01ca4/transformed/jetified-firebase-config-22.0.1/AndroidManifest.xml:25:5-36:19
MERGED from [com.google.firebase:firebase-config:22.0.1] /Users/<USER>/.gradle/caches/transforms-3/be0c224c4da8bf7baeafb21de3f01ca4/transformed/jetified-firebase-config-22.0.1/AndroidManifest.xml:25:5-36:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/1a290a3f05b797b7236a51bcc1d7f52a/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/1a290a3f05b797b7236a51bcc1d7f52a/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/172cbceb397b342c9cf843eb432fc541/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/172cbceb397b342c9cf843eb432fc541/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/transforms-3/43c44d29c9ea2875571e40397afa4dbc/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:21:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/transforms-3/43c44d29c9ea2875571e40397afa4dbc/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:21:5-29:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/96ffa47d0dd9b7416c80de5663a8865a/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/96ffa47d0dd9b7416c80de5663a8865a/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/87fac691493df4f79988be19c1e693b2/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/87fac691493df4f79988be19c1e693b2/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] /Users/<USER>/.gradle/caches/transforms-3/c162869218be1eccef38b435236abde4/transformed/jetified-play-services-fido-20.0.1/AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] /Users/<USER>/.gradle/caches/transforms-3/c162869218be1eccef38b435236abde4/transformed/jetified-play-services-fido-20.0.1/AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] /Users/<USER>/.gradle/caches/transforms-3/bd16f126880ebca356bc63482a1d9c89/transformed/jetified-firebase-appcheck-interop-17.0.0/AndroidManifest.xml:23:5-20
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] /Users/<USER>/.gradle/caches/transforms-3/bd16f126880ebca356bc63482a1d9c89/transformed/jetified-firebase-appcheck-interop-17.0.0/AndroidManifest.xml:23:5-20
MERGED from [com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/transforms-3/44f3b8f078250072ad3e855c1c8d37b1/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/transforms-3/44f3b8f078250072ad3e855c1c8d37b1/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:19:5-23:19
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/357784e7a16ccd95c68cfb70e35fa59d/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/357784e7a16ccd95c68cfb70e35fa59d/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-auth-interop:19.0.2] /Users/<USER>/.gradle/caches/transforms-3/1508eb55e8f880ed69749b98c5d0efc6/transformed/jetified-firebase-auth-interop-19.0.2/AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-auth-interop:19.0.2] /Users/<USER>/.gradle/caches/transforms-3/1508eb55e8f880ed69749b98c5d0efc6/transformed/jetified-firebase-auth-interop-19.0.2/AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] /Users/<USER>/.gradle/caches/transforms-3/8c7ceadf0682a796ab1dc6c947dad809/transformed/jetified-firebase-iid-interop-17.1.0/AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] /Users/<USER>/.gradle/caches/transforms-3/8c7ceadf0682a796ab1dc6c947dad809/transformed/jetified-firebase-iid-interop-17.1.0/AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-abt:21.1.1] /Users/<USER>/.gradle/caches/transforms-3/fed47fe0e911f93eba0838da176bd2db/transformed/jetified-firebase-abt-21.1.1/AndroidManifest.xml:10:5-16:19
MERGED from [com.google.firebase:firebase-abt:21.1.1] /Users/<USER>/.gradle/caches/transforms-3/fed47fe0e911f93eba0838da176bd2db/transformed/jetified-firebase-abt-21.1.1/AndroidManifest.xml:10:5-16:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/transforms-3/8c4c177b5c22dbb533181b6f38d61b8d/transformed/jetified-firebase-measurement-connector-19.0.0/AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/transforms-3/8c4c177b5c22dbb533181b6f38d61b8d/transformed/jetified-firebase-measurement-connector-19.0.0/AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/transforms-3/ab28426cb8a44bb7ef8565ff9d38f5d9/transformed/jetified-play-services-stats-17.0.2/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/transforms-3/ab28426cb8a44bb7ef8565ff9d38f5d9/transformed/jetified-play-services-stats-17.0.2/AndroidManifest.xml:7:5-20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/98a13afb1037d30c765555e4ae128327/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/98a13afb1037d30c765555e4ae128327/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/1a8b478347169530bac076d1af4efc75/transformed/jetified-window-1.2.0/AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/1a8b478347169530bac076d1af4efc75/transformed/jetified-window-1.2.0/AndroidManifest.xml:22:5-29:19
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] /Users/<USER>/.gradle/caches/transforms-3/e0b21fbc1eecde26e0feacc16c1372b1/transformed/jetified-play-services-tasks-18.1.0/AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] /Users/<USER>/.gradle/caches/transforms-3/e0b21fbc1eecde26e0feacc16c1372b1/transformed/jetified-play-services-tasks-18.1.0/AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/transforms-3/6408c3c41b137332f20e3d3bc0428c8c/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/transforms-3/6408c3c41b137332f20e3d3bc0428c8c/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:20:5-24:19
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/a3de342a3f81e3197d28e19f8e01251f/transformed/core-1.13.1/AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/a3de342a3f81e3197d28e19f8e01251f/transformed/core-1.13.1/AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/8bc68417b2d3520d1ae45fa0d3b14d45/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/8bc68417b2d3520d1ae45fa0d3b14d45/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/79b1232db1fbcd0a44e6497dbb9ac93b/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/79b1232db1fbcd0a44e6497dbb9ac93b/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/7309f79d0976d2d00ad66906f83b59f3/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/7309f79d0976d2d00ad66906f83b59f3/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:25:5-39:19
	android:extractNativeLibs
		INJECTED from /Users/<USER>/StudioProjects/test/android/app/src/debug/AndroidManifest.xml
	android:requestLegacyExternalStorage
		ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:13:9-52
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/a3de342a3f81e3197d28e19f8e01251f/transformed/core-1.13.1/AndroidManifest.xml:28:18-86
	android:label
		ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:10:9-41
	android:icon
		ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:12:9-43
	tools:replace
		ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:11:9-38
activity#com.AlKhabeer.MainActivity
ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:15:9-53:20
	android:launchMode
		ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:18:13-43
	android:hardwareAccelerated
		ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:21:13-47
	android:windowSoftInputMode
		ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:22:13-55
	android:exported
		ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:17:13-36
	android:configChanges
		ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:20:13-163
	android:theme
		ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:19:13-47
	android:name
		ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:16:13-41
meta-data#io.flutter.embedding.android.NormalTheme
ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:27:13-30:17
	android:resource
		ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:29:15-52
	android:name
		ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:28:15-70
meta-data#io.flutter.embedding.android.SplashScreenDrawable
ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:36:13-39:17
	android:resource
		ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:38:15-61
	android:name
		ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:37:15-79
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:40:13-43:29
action#android.intent.action.MAIN
ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:41:17-68
	android:name
		ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:41:25-66
category#android.intent.category.LAUNCHER
ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:42:17-76
	android:name
		ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:42:27-74
meta-data#flutter_deeplinking_enabled
ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:45:13-90
	android:value
		ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:45:67-87
	android:name
		ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:45:24-66
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:alkhabeer.com+data:scheme:alkhabeer
ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:46:13-51:29
	android:autoVerify
		ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:46:28-53
action#android.intent.action.VIEW
ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:47:17-69
	android:name
		ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:47:25-66
category#android.intent.category.DEFAULT
ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:48:17-76
	android:name
		ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:48:27-73
category#android.intent.category.BROWSABLE
ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:49:17-78
	android:name
		ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:49:27-75
data
ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:50:17-81
	android:host
		ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:50:50-78
	android:scheme
		ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:50:23-49
meta-data#flutterEmbedding
ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:57:9-59:33
	android:value
		ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:59:13-30
	android:name
		ADDED from /Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:58:13-44
uses-sdk
INJECTED from /Users/<USER>/StudioProjects/test/android/app/src/debug/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/StudioProjects/test/android/app/src/debug/AndroidManifest.xml
INJECTED from /Users/<USER>/StudioProjects/test/android/app/src/debug/AndroidManifest.xml
MERGED from [:app_links] /Users/<USER>/StudioProjects/test/build/app_links/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:app_links] /Users/<USER>/StudioProjects/test/build/app_links/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:cloud_firestore] /Users/<USER>/StudioProjects/test/build/cloud_firestore/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:cloud_firestore] /Users/<USER>/StudioProjects/test/build/cloud_firestore/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:file_picker] /Users/<USER>/StudioProjects/test/build/file_picker/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:file_picker] /Users/<USER>/StudioProjects/test/build/file_picker/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:firebase_performance] /Users/<USER>/StudioProjects/test/build/firebase_performance/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:firebase_performance] /Users/<USER>/StudioProjects/test/build/firebase_performance/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] /Users/<USER>/StudioProjects/test/build/firebase_core/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] /Users/<USER>/StudioProjects/test/build/firebase_core/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:6:5-44
MERGED from [:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:6:5-44
MERGED from [:flutter_plugin_android_lifecycle] /Users/<USER>/StudioProjects/test/build/flutter_plugin_android_lifecycle/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] /Users/<USER>/StudioProjects/test/build/flutter_plugin_android_lifecycle/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:google_sign_in_android] /Users/<USER>/StudioProjects/test/build/google_sign_in_android/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:google_sign_in_android] /Users/<USER>/StudioProjects/test/build/google_sign_in_android/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] /Users/<USER>/StudioProjects/test/build/path_provider_android/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] /Users/<USER>/StudioProjects/test/build/path_provider_android/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] /Users/<USER>/StudioProjects/test/build/shared_preferences_android/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] /Users/<USER>/StudioProjects/test/build/shared_preferences_android/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:sign_in_with_apple] /Users/<USER>/StudioProjects/test/build/sign_in_with_apple/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:sign_in_with_apple] /Users/<USER>/StudioProjects/test/build/sign_in_with_apple/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:sqflite] /Users/<USER>/StudioProjects/test/build/sqflite/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:sqflite] /Users/<USER>/StudioProjects/test/build/sqflite/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] /Users/<USER>/StudioProjects/test/build/url_launcher_android/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] /Users/<USER>/StudioProjects/test/build/url_launcher_android/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:video_player_android] /Users/<USER>/StudioProjects/test/build/video_player_android/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:video_player_android] /Users/<USER>/StudioProjects/test/build/video_player_android/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:webview_flutter_android] /Users/<USER>/StudioProjects/test/build/webview_flutter_android/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [:webview_flutter_android] /Users/<USER>/StudioProjects/test/build/webview_flutter_android/intermediates/merged_manifest/debug/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-perf:21.0.2] /Users/<USER>/.gradle/caches/transforms-3/b863a0e80e78acee3b999a288117c346/transformed/jetified-firebase-perf-21.0.2/AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-perf:21.0.2] /Users/<USER>/.gradle/caches/transforms-3/b863a0e80e78acee3b999a288117c346/transformed/jetified-firebase-perf-21.0.2/AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-sessions:2.0.0] /Users/<USER>/.gradle/caches/transforms-3/7243a85cae0957d4f2c115b4e3710992/transformed/jetified-firebase-sessions-2.0.0/AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-sessions:2.0.0] /Users/<USER>/.gradle/caches/transforms-3/7243a85cae0957d4f2c115b4e3710992/transformed/jetified-firebase-sessions-2.0.0/AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-firestore:25.1.1] /Users/<USER>/.gradle/caches/transforms-3/530d6bd4293568127cdd25ad75f71303/transformed/jetified-firebase-firestore-25.1.1/AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-firestore:25.1.1] /Users/<USER>/.gradle/caches/transforms-3/530d6bd4293568127cdd25ad75f71303/transformed/jetified-firebase-firestore-25.1.1/AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-config:22.0.1] /Users/<USER>/.gradle/caches/transforms-3/be0c224c4da8bf7baeafb21de3f01ca4/transformed/jetified-firebase-config-22.0.1/AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-config:22.0.1] /Users/<USER>/.gradle/caches/transforms-3/be0c224c4da8bf7baeafb21de3f01ca4/transformed/jetified-firebase-config-22.0.1/AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/1a290a3f05b797b7236a51bcc1d7f52a/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/1a290a3f05b797b7236a51bcc1d7f52a/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/172cbceb397b342c9cf843eb432fc541/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/172cbceb397b342c9cf843eb432fc541/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-config-interop:16.0.1] /Users/<USER>/.gradle/caches/transforms-3/c145f584d00a811dbc467b0f60d74396/transformed/jetified-firebase-config-interop-16.0.1/AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-config-interop:16.0.1] /Users/<USER>/.gradle/caches/transforms-3/c145f584d00a811dbc467b0f60d74396/transformed/jetified-firebase-config-interop-16.0.1/AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/transforms-3/43c44d29c9ea2875571e40397afa4dbc/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/transforms-3/43c44d29c9ea2875571e40397afa4dbc/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/96ffa47d0dd9b7416c80de5663a8865a/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/96ffa47d0dd9b7416c80de5663a8865a/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.1] /Users/<USER>/.gradle/caches/transforms-3/2adf4a250f618d3d504407f438449213/transformed/jetified-firebase-encoders-json-18.0.1/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.1] /Users/<USER>/.gradle/caches/transforms-3/2adf4a250f618d3d504407f438449213/transformed/jetified-firebase-encoders-json-18.0.1/AndroidManifest.xml:18:5-20:41
MERGED from [androidx.datastore:datastore-preferences:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/b720a443358c871b0022dcf4745b1542/transformed/jetified-datastore-preferences-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore-preferences:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/b720a443358c871b0022dcf4745b1542/transformed/jetified-datastore-preferences-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/8913d7690b261cf7123c3be7247fc1fb/transformed/jetified-datastore-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/8913d7690b261cf7123c3be7247fc1fb/transformed/jetified-datastore-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/87fac691493df4f79988be19c1e693b2/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/87fac691493df4f79988be19c1e693b2/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] /Users/<USER>/.gradle/caches/transforms-3/0e835b4afdda5af2322da9e33afcefa3/transformed/jetified-play-services-auth-api-phone-18.0.2/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] /Users/<USER>/.gradle/caches/transforms-3/0e835b4afdda5af2322da9e33afcefa3/transformed/jetified-play-services-auth-api-phone-18.0.2/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] /Users/<USER>/.gradle/caches/transforms-3/7b58ec1d7a2add5a173714af0a87c160/transformed/jetified-play-services-auth-base-18.0.10/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] /Users/<USER>/.gradle/caches/transforms-3/7b58ec1d7a2add5a173714af0a87c160/transformed/jetified-play-services-auth-base-18.0.10/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] /Users/<USER>/.gradle/caches/transforms-3/c162869218be1eccef38b435236abde4/transformed/jetified-play-services-fido-20.0.1/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] /Users/<USER>/.gradle/caches/transforms-3/c162869218be1eccef38b435236abde4/transformed/jetified-play-services-fido-20.0.1/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] /Users/<USER>/.gradle/caches/transforms-3/bd16f126880ebca356bc63482a1d9c89/transformed/jetified-firebase-appcheck-interop-17.0.0/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] /Users/<USER>/.gradle/caches/transforms-3/bd16f126880ebca356bc63482a1d9c89/transformed/jetified-firebase-appcheck-interop-17.0.0/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] /Users/<USER>/.gradle/caches/transforms-3/d5860f6dcea01ec41b8928a37336e6cd/transformed/jetified-firebase-database-collection-18.0.1/AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] /Users/<USER>/.gradle/caches/transforms-3/d5860f6dcea01ec41b8928a37336e6cd/transformed/jetified-firebase-database-collection-18.0.1/AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/transforms-3/44f3b8f078250072ad3e855c1c8d37b1/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/transforms-3/44f3b8f078250072ad3e855c1c8d37b1/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:18:5-43
MERGED from [androidx.appcompat:appcompat:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/953d05e3fa95b7ff82a1fd5ab5bbc38e/transformed/appcompat-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/953d05e3fa95b7ff82a1fd5ab5bbc38e/transformed/appcompat-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/357784e7a16ccd95c68cfb70e35fa59d/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/357784e7a16ccd95c68cfb70e35fa59d/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-auth-interop:19.0.2] /Users/<USER>/.gradle/caches/transforms-3/1508eb55e8f880ed69749b98c5d0efc6/transformed/jetified-firebase-auth-interop-19.0.2/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:19.0.2] /Users/<USER>/.gradle/caches/transforms-3/1508eb55e8f880ed69749b98c5d0efc6/transformed/jetified-firebase-auth-interop-19.0.2/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] /Users/<USER>/.gradle/caches/transforms-3/8c7ceadf0682a796ab1dc6c947dad809/transformed/jetified-firebase-iid-interop-17.1.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] /Users/<USER>/.gradle/caches/transforms-3/8c7ceadf0682a796ab1dc6c947dad809/transformed/jetified-firebase-iid-interop-17.1.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-abt:21.1.1] /Users/<USER>/.gradle/caches/transforms-3/fed47fe0e911f93eba0838da176bd2db/transformed/jetified-firebase-abt-21.1.1/AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-abt:21.1.1] /Users/<USER>/.gradle/caches/transforms-3/fed47fe0e911f93eba0838da176bd2db/transformed/jetified-firebase-abt-21.1.1/AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/transforms-3/8c4c177b5c22dbb533181b6f38d61b8d/transformed/jetified-firebase-measurement-connector-19.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/transforms-3/8c4c177b5c22dbb533181b6f38d61b8d/transformed/jetified-firebase-measurement-connector-19.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/63a2baf8c135443c6637edfc9d04aed6/transformed/jetified-play-services-cloud-messaging-17.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/63a2baf8c135443c6637edfc9d04aed6/transformed/jetified-play-services-cloud-messaging-17.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/transforms-3/ab28426cb8a44bb7ef8565ff9d38f5d9/transformed/jetified-play-services-stats-17.0.2/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/transforms-3/ab28426cb8a44bb7ef8565ff9d38f5d9/transformed/jetified-play-services-stats-17.0.2/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] /Users/<USER>/.gradle/caches/transforms-3/920afd2299c726bbedf590bb865fea5e/transformed/jetified-firebase-installations-interop-17.1.1/AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] /Users/<USER>/.gradle/caches/transforms-3/920afd2299c726bbedf590bb865fea5e/transformed/jetified-firebase-installations-interop-17.1.1/AndroidManifest.xml:17:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/cf023b4654ab7eda9cfdca5c6b56e762/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/cf023b4654ab7eda9cfdca5c6b56e762/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/d692ab5775189a752d3ebc49b04d52ec/transformed/loader-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/d692ab5775189a752d3ebc49b04d52ec/transformed/loader-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/aeb460594c9750ecf4d241b7622c57eb/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/aeb460594c9750ecf4d241b7622c57eb/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/0d724274b46780f68492593c5138c21e/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/0d724274b46780f68492593c5138c21e/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/98a13afb1037d30c765555e4ae128327/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/98a13afb1037d30c765555e4ae128327/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/6975cc431ec8498ffdf2796b67759ca0/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/6975cc431ec8498ffdf2796b67759ca0/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/d29f4b4a0b5f264567f654ef8608c1f5/transformed/jetified-window-java-1.2.0/AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/d29f4b4a0b5f264567f654ef8608c1f5/transformed/jetified-window-java-1.2.0/AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/1a8b478347169530bac076d1af4efc75/transformed/jetified-window-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/1a8b478347169530bac076d1af4efc75/transformed/jetified-window-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] /Users/<USER>/.gradle/caches/transforms-3/e0b21fbc1eecde26e0feacc16c1372b1/transformed/jetified-play-services-tasks-18.1.0/AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] /Users/<USER>/.gradle/caches/transforms-3/e0b21fbc1eecde26e0feacc16c1372b1/transformed/jetified-play-services-tasks-18.1.0/AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/transforms-3/6408c3c41b137332f20e3d3bc0428c8c/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/transforms-3/6408c3c41b137332f20e3d3bc0428c8c/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.7.1] /Users/<USER>/.gradle/caches/transforms-3/1125d6a8eabbe5b42dadf26115c520ee/transformed/fragment-1.7.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] /Users/<USER>/.gradle/caches/transforms-3/1125d6a8eabbe5b42dadf26115c520ee/transformed/fragment-1.7.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.9.1] /Users/<USER>/.gradle/caches/transforms-3/6fbe174a0d7b8561e64c5b995ae99f9f/transformed/jetified-activity-1.9.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.1] /Users/<USER>/.gradle/caches/transforms-3/6fbe174a0d7b8561e64c5b995ae99f9f/transformed/jetified-activity-1.9.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.browser:browser:1.8.0] /Users/<USER>/.gradle/caches/transforms-3/86ee45f5c791d8d00fa99ae2b84aaec9/transformed/browser-1.8.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] /Users/<USER>/.gradle/caches/transforms-3/86ee45f5c791d8d00fa99ae2b84aaec9/transformed/browser-1.8.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.media3:media3-extractor:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/8cad0cadf2eff4aeeabb85714040df6f/transformed/jetified-media3-extractor-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/8cad0cadf2eff4aeeabb85714040df6f/transformed/jetified-media3-extractor-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/c74f2068ed98f20276cbfc6886ee9610/transformed/jetified-media3-container-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/c74f2068ed98f20276cbfc6886ee9610/transformed/jetified-media3-container-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/11f98baa43b386be292468958e7a4230/transformed/jetified-media3-datasource-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/11f98baa43b386be292468958e7a4230/transformed/jetified-media3-datasource-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/1d28d14250a5a46382d8ea8d6ef95c81/transformed/jetified-media3-decoder-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/1d28d14250a5a46382d8ea8d6ef95c81/transformed/jetified-media3-decoder-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/2a1536eae5f5020b732ac916ee2f951a/transformed/jetified-media3-database-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/2a1536eae5f5020b732ac916ee2f951a/transformed/jetified-media3-database-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/53101df6edb2b8fbeb4cd06a735b22ad/transformed/jetified-media3-common-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/53101df6edb2b8fbeb4cd06a735b22ad/transformed/jetified-media3-common-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/dea0a95a2c925b9a749a72f8dfade75f/transformed/jetified-media3-exoplayer-hls-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/dea0a95a2c925b9a749a72f8dfade75f/transformed/jetified-media3-exoplayer-hls-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/92a976be9b00372484ead900aeb17dd5/transformed/jetified-media3-exoplayer-dash-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/92a976be9b00372484ead900aeb17dd5/transformed/jetified-media3-exoplayer-dash-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-rtsp:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/9cceb47dae5cb8dea4613d76b0f6ecd6/transformed/jetified-media3-exoplayer-rtsp-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-rtsp:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/9cceb47dae5cb8dea4613d76b0f6ecd6/transformed/jetified-media3-exoplayer-rtsp-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/9245854b7846ebddcb972974fbb29333/transformed/jetified-media3-exoplayer-smoothstreaming-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-smoothstreaming:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/9245854b7846ebddcb972974fbb29333/transformed/jetified-media3-exoplayer-smoothstreaming-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/62e4a06f332e44aa23e2085894e571ac/transformed/jetified-media3-exoplayer-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/62e4a06f332e44aa23e2085894e571ac/transformed/jetified-media3-exoplayer-1.4.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.webkit:webkit:1.11.0] /Users/<USER>/.gradle/caches/transforms-3/5a545179b0d0210524bd88420d5fad40/transformed/webkit-1.11.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.11.0] /Users/<USER>/.gradle/caches/transforms-3/5a545179b0d0210524bd88420d5fad40/transformed/webkit-1.11.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/18183d2636cbce3a4f21b0eafd05aafb/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/18183d2636cbce3a4f21b0eafd05aafb/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/1605e6ec768bd6e9706a2268045ef7db/transformed/jetified-core-ktx-1.13.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/1605e6ec768bd6e9706a2268045ef7db/transformed/jetified-core-ktx-1.13.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/58df69eefc1e2025970934b53128ba84/transformed/drawerlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/58df69eefc1e2025970934b53128ba84/transformed/drawerlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/2af298b413dea7157a95380e484c9fe8/transformed/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/2af298b413dea7157a95380e484c9fe8/transformed/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/2af213816ce04d40f36d51728a91bf19/transformed/jetified-appcompat-resources-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/2af213816ce04d40f36d51728a91bf19/transformed/jetified-appcompat-resources-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/b202e5b322aa44017154942c3e3ecf50/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/b202e5b322aa44017154942c3e3ecf50/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/4b42d40141582d0bce47d3a0ed91d6d3/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/4b42d40141582d0bce47d3a0ed91d6d3/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/a3de342a3f81e3197d28e19f8e01251f/transformed/core-1.13.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/a3de342a3f81e3197d28e19f8e01251f/transformed/core-1.13.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/d8089a24dc295780ff7ab5b60620a9e5/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/d8089a24dc295780ff7ab5b60620a9e5/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/transforms-3/c5506762b7ddec4c3f17606aa1c6ab04/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/transforms-3/c5506762b7ddec4c3f17606aa1c6ab04/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/40fcdb2fe9907dd26d5d7b998f10b9f4/transformed/localbroadcastmanager-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] /Users/<USER>/.gradle/caches/transforms-3/40fcdb2fe9907dd26d5d7b998f10b9f4/transformed/localbroadcastmanager-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] /Users/<USER>/.gradle/caches/transforms-3/9f4b17e0e9be6f79a754a7bca430ad87/transformed/exifinterface-1.3.7/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] /Users/<USER>/.gradle/caches/transforms-3/9f4b17e0e9be6f79a754a7bca430ad87/transformed/exifinterface-1.3.7/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/8bc68417b2d3520d1ae45fa0d3b14d45/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/8bc68417b2d3520d1ae45fa0d3b14d45/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/7ec59fd88e2ebab119ff7cd6fa5e9583/transformed/jetified-tracing-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/7ec59fd88e2ebab119ff7cd6fa5e9583/transformed/jetified-tracing-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/62ee97de108aafd046aef50d76128dfd/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/62ee97de108aafd046aef50d76128dfd/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/79b1232db1fbcd0a44e6497dbb9ac93b/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/79b1232db1fbcd0a44e6497dbb9ac93b/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/transforms-3/b4e148e8fdd16144492c40b53ee11bd9/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/transforms-3/b4e148e8fdd16144492c40b53ee11bd9/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/bb3745cbc6a48c9f3e1477d3fdacee76/transformed/jetified-firebase-components-18.0.0/AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/bb3745cbc6a48c9f3e1477d3fdacee76/transformed/jetified-firebase-components-18.0.0/AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/7309f79d0976d2d00ad66906f83b59f3/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/7309f79d0976d2d00ad66906f83b59f3/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.1.0] /Users/<USER>/.gradle/caches/transforms-3/d9fa67c2541d7cf24178998779ce82f6/transformed/jetified-transport-api-3.1.0/AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.1.0] /Users/<USER>/.gradle/caches/transforms-3/d9fa67c2541d7cf24178998779ce82f6/transformed/jetified-transport-api-3.1.0/AndroidManifest.xml:18:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/cd42dcb6a1fe8bf7708f7a71548d8de6/transformed/jetified-core-1.0.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/cd42dcb6a1fe8bf7708f7a71548d8de6/transformed/jetified-core-1.0.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/0822bdcc5aeb03272bc4320a44285100/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/0822bdcc5aeb03272bc4320a44285100/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/8bc26783ec4195e6f1c3d720ed8f9925/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/8bc26783ec4195e6f1c3d720ed8f9925/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/07be7164f23f4ca322e8272a0c413f2c/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/transforms-3/07be7164f23f4ca322e8272a0c413f2c/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [org.microg:safe-parcel:1.7.0] /Users/<USER>/.gradle/caches/transforms-3/2b33262094d94d701b641b5df2c979b7/transformed/jetified-safe-parcel-1.7.0/AndroidManifest.xml:10:5-12:41
MERGED from [org.microg:safe-parcel:1.7.0] /Users/<USER>/.gradle/caches/transforms-3/2b33262094d94d701b641b5df2c979b7/transformed/jetified-safe-parcel-1.7.0/AndroidManifest.xml:10:5-12:41
MERGED from [io.grpc:grpc-android:1.62.2] /Users/<USER>/.gradle/caches/transforms-3/6d72ee594cdad76bd739f1792ad4c6ba/transformed/jetified-grpc-android-1.62.2/AndroidManifest.xml:5:5-44
MERGED from [io.grpc:grpc-android:1.62.2] /Users/<USER>/.gradle/caches/transforms-3/6d72ee594cdad76bd739f1792ad4c6ba/transformed/jetified-grpc-android-1.62.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] /Users/<USER>/.gradle/caches/transforms-3/20b2dca2f480923336e8ef7b085e6e58/transformed/jetified-annotation-experimental-1.4.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] /Users/<USER>/.gradle/caches/transforms-3/20b2dca2f480923336e8ef7b085e6e58/transformed/jetified-annotation-experimental-1.4.0/AndroidManifest.xml:5:5-44
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] /Users/<USER>/.gradle/caches/transforms-3/d8b41ce662ae51121ec0b03c25271f69/transformed/jetified-relinker-1.4.5/AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] /Users/<USER>/.gradle/caches/transforms-3/d8b41ce662ae51121ec0b03c25271f69/transformed/jetified-relinker-1.4.5/AndroidManifest.xml:5:5-43
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/78188d12c9adb696ed6c96facd320437/transformed/jetified-protolite-well-known-types-18.0.0/AndroidManifest.xml:7:5-9:41
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/78188d12c9adb696ed6c96facd320437/transformed/jetified-protolite-well-known-types-18.0.0/AndroidManifest.xml:7:5-9:41
	android:targetSdkVersion
		INJECTED from /Users/<USER>/StudioProjects/test/android/app/src/debug/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/StudioProjects/test/android/app/src/debug/AndroidManifest.xml
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [:cloud_firestore] /Users/<USER>/StudioProjects/test/build/cloud_firestore/intermediates/merged_manifest/debug/AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:35:9-39:19
MERGED from [:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:35:9-39:19
MERGED from [:firebase_performance] /Users/<USER>/StudioProjects/test/build/firebase_performance/intermediates/merged_manifest/debug/AndroidManifest.xml:10:9-14:19
MERGED from [:firebase_performance] /Users/<USER>/StudioProjects/test/build/firebase_performance/intermediates/merged_manifest/debug/AndroidManifest.xml:10:9-14:19
MERGED from [:firebase_core] /Users/<USER>/StudioProjects/test/build/firebase_core/intermediates/merged_manifest/debug/AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_core] /Users/<USER>/StudioProjects/test/build/firebase_core/intermediates/merged_manifest/debug/AndroidManifest.xml:8:9-12:19
MERGED from [com.google.firebase:firebase-perf:21.0.2] /Users/<USER>/.gradle/caches/transforms-3/b863a0e80e78acee3b999a288117c346/transformed/jetified-firebase-perf-21.0.2/AndroidManifest.xml:11:9-20:19
MERGED from [com.google.firebase:firebase-perf:21.0.2] /Users/<USER>/.gradle/caches/transforms-3/b863a0e80e78acee3b999a288117c346/transformed/jetified-firebase-perf-21.0.2/AndroidManifest.xml:11:9-20:19
MERGED from [com.google.firebase:firebase-sessions:2.0.0] /Users/<USER>/.gradle/caches/transforms-3/7243a85cae0957d4f2c115b4e3710992/transformed/jetified-firebase-sessions-2.0.0/AndroidManifest.xml:26:9-32:19
MERGED from [com.google.firebase:firebase-sessions:2.0.0] /Users/<USER>/.gradle/caches/transforms-3/7243a85cae0957d4f2c115b4e3710992/transformed/jetified-firebase-sessions-2.0.0/AndroidManifest.xml:26:9-32:19
MERGED from [com.google.firebase:firebase-firestore:25.1.1] /Users/<USER>/.gradle/caches/transforms-3/530d6bd4293568127cdd25ad75f71303/transformed/jetified-firebase-firestore-25.1.1/AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-firestore:25.1.1] /Users/<USER>/.gradle/caches/transforms-3/530d6bd4293568127cdd25ad75f71303/transformed/jetified-firebase-firestore-25.1.1/AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-config:22.0.1] /Users/<USER>/.gradle/caches/transforms-3/be0c224c4da8bf7baeafb21de3f01ca4/transformed/jetified-firebase-config-22.0.1/AndroidManifest.xml:26:9-35:19
MERGED from [com.google.firebase:firebase-config:22.0.1] /Users/<USER>/.gradle/caches/transforms-3/be0c224c4da8bf7baeafb21de3f01ca4/transformed/jetified-firebase-config-22.0.1/AndroidManifest.xml:26:9-35:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/1a290a3f05b797b7236a51bcc1d7f52a/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/1a290a3f05b797b7236a51bcc1d7f52a/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/172cbceb397b342c9cf843eb432fc541/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/172cbceb397b342c9cf843eb432fc541/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/transforms-3/43c44d29c9ea2875571e40397afa4dbc/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/transforms-3/43c44d29c9ea2875571e40397afa4dbc/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/357784e7a16ccd95c68cfb70e35fa59d/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/357784e7a16ccd95c68cfb70e35fa59d/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-abt:21.1.1] /Users/<USER>/.gradle/caches/transforms-3/fed47fe0e911f93eba0838da176bd2db/transformed/jetified-firebase-abt-21.1.1/AndroidManifest.xml:11:9-15:19
MERGED from [com.google.firebase:firebase-abt:21.1.1] /Users/<USER>/.gradle/caches/transforms-3/fed47fe0e911f93eba0838da176bd2db/transformed/jetified-firebase-abt-21.1.1/AndroidManifest.xml:11:9-15:19
	android:exported
		ADDED from [com.google.firebase:firebase-perf:21.0.2] /Users/<USER>/.gradle/caches/transforms-3/b863a0e80e78acee3b999a288117c346/transformed/jetified-firebase-perf-21.0.2/AndroidManifest.xml:13:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/357784e7a16ccd95c68cfb70e35fa59d/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/357784e7a16ccd95c68cfb70e35fa59d/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:32:13-43
	android:name
		ADDED from [:cloud_firestore] /Users/<USER>/StudioProjects/test/build/cloud_firestore/intermediates/merged_manifest/debug/AndroidManifest.xml:8:18-89
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.firestore.FlutterFirebaseFirestoreRegistrar
ADDED from [:cloud_firestore] /Users/<USER>/StudioProjects/test/build/cloud_firestore/intermediates/merged_manifest/debug/AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:cloud_firestore] /Users/<USER>/StudioProjects/test/build/cloud_firestore/intermediates/merged_manifest/debug/AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:cloud_firestore] /Users/<USER>/StudioProjects/test/build/cloud_firestore/intermediates/merged_manifest/debug/AndroidManifest.xml:10:17-134
queries
ADDED from [:file_picker] /Users/<USER>/StudioProjects/test/build/file_picker/intermediates/merged_manifest/debug/AndroidManifest.xml:11:5-17:15
intent#action:name:android.intent.action.GET_CONTENT+data:mimeType:*/*
ADDED from [:file_picker] /Users/<USER>/StudioProjects/test/build/file_picker/intermediates/merged_manifest/debug/AndroidManifest.xml:12:9-16:18
action#android.intent.action.GET_CONTENT
ADDED from [:file_picker] /Users/<USER>/StudioProjects/test/build/file_picker/intermediates/merged_manifest/debug/AndroidManifest.xml:13:13-72
	android:name
		ADDED from [:file_picker] /Users/<USER>/StudioProjects/test/build/file_picker/intermediates/merged_manifest/debug/AndroidManifest.xml:13:21-69
uses-permission#android.permission.WAKE_LOCK
ADDED from [:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:8:5-68
MERGED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/63a2baf8c135443c6637edfc9d04aed6/transformed/jetified-play-services-cloud-messaging-17.2.0/AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/63a2baf8c135443c6637edfc9d04aed6/transformed/jetified-play-services-cloud-messaging-17.2.0/AndroidManifest.xml:9:5-68
	android:name
		ADDED from [:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:8:22-65
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:9:5-79
MERGED from [com.google.firebase:firebase-firestore:25.1.1] /Users/<USER>/.gradle/caches/transforms-3/530d6bd4293568127cdd25ad75f71303/transformed/jetified-firebase-firestore-25.1.1/AndroidManifest.xml:10:5-79
MERGED from [com.google.firebase:firebase-firestore:25.1.1] /Users/<USER>/.gradle/caches/transforms-3/530d6bd4293568127cdd25ad75f71303/transformed/jetified-firebase-firestore-25.1.1/AndroidManifest.xml:10:5-79
MERGED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-config:22.0.1] /Users/<USER>/.gradle/caches/transforms-3/be0c224c4da8bf7baeafb21de3f01ca4/transformed/jetified-firebase-config-22.0.1/AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-config:22.0.1] /Users/<USER>/.gradle/caches/transforms-3/be0c224c4da8bf7baeafb21de3f01ca4/transformed/jetified-firebase-config-22.0.1/AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/1a290a3f05b797b7236a51bcc1d7f52a/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/1a290a3f05b797b7236a51bcc1d7f52a/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:7:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/96ffa47d0dd9b7416c80de5663a8865a/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/96ffa47d0dd9b7416c80de5663a8865a/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/63a2baf8c135443c6637edfc9d04aed6/transformed/jetified-play-services-cloud-messaging-17.2.0/AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/63a2baf8c135443c6637edfc9d04aed6/transformed/jetified-play-services-cloud-messaging-17.2.0/AndroidManifest.xml:7:5-79
MERGED from [androidx.media3:media3-common:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/53101df6edb2b8fbeb4cd06a735b22ad/transformed/jetified-media3-common-1.4.1/AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/53101df6edb2b8fbeb4cd06a735b22ad/transformed/jetified-media3-common-1.4.1/AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/62e4a06f332e44aa23e2085894e571ac/transformed/jetified-media3-exoplayer-1.4.1/AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.4.1] /Users/<USER>/.gradle/caches/transforms-3/62e4a06f332e44aa23e2085894e571ac/transformed/jetified-media3-exoplayer-1.4.1/AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/7309f79d0976d2d00ad66906f83b59f3/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/7309f79d0976d2d00ad66906f83b59f3/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:22:5-79
MERGED from [io.grpc:grpc-android:1.62.2] /Users/<USER>/.gradle/caches/transforms-3/6d72ee594cdad76bd739f1792ad4c6ba/transformed/jetified-grpc-android-1.62.2/AndroidManifest.xml:7:5-79
MERGED from [io.grpc:grpc-android:1.62.2] /Users/<USER>/.gradle/caches/transforms-3/6d72ee594cdad76bd739f1792ad4c6ba/transformed/jetified-grpc-android-1.62.2/AndroidManifest.xml:7:5-79
	android:name
		ADDED from [:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:9:22-76
service#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService
ADDED from [:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:14:9-17:72
	android:exported
		ADDED from [:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:16:13-37
	android:permission
		ADDED from [:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:17:13-69
	android:name
		ADDED from [:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:15:13-107
service#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService
ADDED from [:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:18:9-24:19
	android:exported
		ADDED from [:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:20:13-37
	android:name
		ADDED from [:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:19:13-97
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from [:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:21:13-23:29
action#com.google.firebase.MESSAGING_EVENT
ADDED from [:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:22:17-78
	android:name
		ADDED from [:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:22:25-75
receiver#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver
ADDED from [:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:26:9-33:20
	android:exported
		ADDED from [:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:28:13-36
	android:permission
		ADDED from [:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:29:13-73
	android:name
		ADDED from [:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:27:13-98
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:30:13-32:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:31:17-81
	android:name
		ADDED from [:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:31:25-78
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar
ADDED from [:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:36:13-38:85
	android:value
		ADDED from [:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:38:17-82
	android:name
		ADDED from [:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:37:17-128
provider#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider
ADDED from [:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:41:9-45:38
	android:authorities
		ADDED from [:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:43:13-88
	android:exported
		ADDED from [:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:44:13-37
	android:initOrder
		ADDED from [:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:45:13-35
	android:name
		ADDED from [:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:42:13-102
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.performance.FlutterFirebaseAppRegistrar
ADDED from [:firebase_performance] /Users/<USER>/StudioProjects/test/build/firebase_performance/intermediates/merged_manifest/debug/AndroidManifest.xml:11:13-13:85
	android:value
		ADDED from [:firebase_performance] /Users/<USER>/StudioProjects/test/build/firebase_performance/intermediates/merged_manifest/debug/AndroidManifest.xml:13:17-82
	android:name
		ADDED from [:firebase_performance] /Users/<USER>/StudioProjects/test/build/firebase_performance/intermediates/merged_manifest/debug/AndroidManifest.xml:12:17-130
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar
ADDED from [:firebase_core] /Users/<USER>/StudioProjects/test/build/firebase_core/intermediates/merged_manifest/debug/AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:firebase_core] /Users/<USER>/StudioProjects/test/build/firebase_core/intermediates/merged_manifest/debug/AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:firebase_core] /Users/<USER>/StudioProjects/test/build/firebase_core/intermediates/merged_manifest/debug/AndroidManifest.xml:10:17-124
provider#io.flutter.plugins.imagepicker.ImagePickerFileProvider
ADDED from [:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:9:9-17:20
	android:grantUriPermissions
		ADDED from [:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:13:13-47
	android:authorities
		ADDED from [:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:11:13-74
	android:exported
		ADDED from [:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:12:13-37
	android:name
		ADDED from [:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:10:13-82
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:14:13-16:75
	android:resource
		ADDED from [:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:16:17-72
	android:name
		ADDED from [:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:15:17-67
service#com.google.android.gms.metadata.ModuleDependencies
ADDED from [:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:19:9-31:19
	android:enabled
		ADDED from [:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:21:13-36
	android:exported
		ADDED from [:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:22:13-37
	tools:ignore
		ADDED from [:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:23:13-40
	android:name
		ADDED from [:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:20:13-78
intent-filter#action:name:com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:24:13-26:29
action#com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:25:17-94
	android:name
		ADDED from [:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:25:25-91
meta-data#photopicker_activity:0:required
ADDED from [:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:28:13-30:36
	android:value
		ADDED from [:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:30:17-33
	android:name
		ADDED from [:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:29:17-63
activity#io.flutter.plugins.urllauncher.WebViewActivity
ADDED from [:url_launcher_android] /Users/<USER>/StudioProjects/test/build/url_launcher_android/intermediates/merged_manifest/debug/AndroidManifest.xml:8:9-11:74
	android:exported
		ADDED from [:url_launcher_android] /Users/<USER>/StudioProjects/test/build/url_launcher_android/intermediates/merged_manifest/debug/AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [:url_launcher_android] /Users/<USER>/StudioProjects/test/build/url_launcher_android/intermediates/merged_manifest/debug/AndroidManifest.xml:11:13-71
	android:name
		ADDED from [:url_launcher_android] /Users/<USER>/StudioProjects/test/build/url_launcher_android/intermediates/merged_manifest/debug/AndroidManifest.xml:9:13-74
meta-data#com.google.firebase.components:com.google.firebase.perf.FirebasePerfKtxRegistrar
ADDED from [com.google.firebase:firebase-perf:21.0.2] /Users/<USER>/.gradle/caches/transforms-3/b863a0e80e78acee3b999a288117c346/transformed/jetified-firebase-perf-21.0.2/AndroidManifest.xml:14:13-16:85
	android:value
		ADDED from [com.google.firebase:firebase-perf:21.0.2] /Users/<USER>/.gradle/caches/transforms-3/b863a0e80e78acee3b999a288117c346/transformed/jetified-firebase-perf-21.0.2/AndroidManifest.xml:16:17-82
	android:name
		ADDED from [com.google.firebase:firebase-perf:21.0.2] /Users/<USER>/.gradle/caches/transforms-3/b863a0e80e78acee3b999a288117c346/transformed/jetified-firebase-perf-21.0.2/AndroidManifest.xml:15:17-112
meta-data#com.google.firebase.components:com.google.firebase.perf.FirebasePerfRegistrar
ADDED from [com.google.firebase:firebase-perf:21.0.2] /Users/<USER>/.gradle/caches/transforms-3/b863a0e80e78acee3b999a288117c346/transformed/jetified-firebase-perf-21.0.2/AndroidManifest.xml:17:13-19:85
	android:value
		ADDED from [com.google.firebase:firebase-perf:21.0.2] /Users/<USER>/.gradle/caches/transforms-3/b863a0e80e78acee3b999a288117c346/transformed/jetified-firebase-perf-21.0.2/AndroidManifest.xml:19:17-82
	android:name
		ADDED from [com.google.firebase:firebase-perf:21.0.2] /Users/<USER>/.gradle/caches/transforms-3/b863a0e80e78acee3b999a288117c346/transformed/jetified-firebase-perf-21.0.2/AndroidManifest.xml:18:17-109
service#com.google.firebase.sessions.SessionLifecycleService
ADDED from [com.google.firebase:firebase-sessions:2.0.0] /Users/<USER>/.gradle/caches/transforms-3/7243a85cae0957d4f2c115b4e3710992/transformed/jetified-firebase-sessions-2.0.0/AndroidManifest.xml:22:9-25:40
	android:enabled
		ADDED from [com.google.firebase:firebase-sessions:2.0.0] /Users/<USER>/.gradle/caches/transforms-3/7243a85cae0957d4f2c115b4e3710992/transformed/jetified-firebase-sessions-2.0.0/AndroidManifest.xml:24:13-35
	android:exported
		ADDED from [com.google.firebase:firebase-sessions:2.0.0] /Users/<USER>/.gradle/caches/transforms-3/7243a85cae0957d4f2c115b4e3710992/transformed/jetified-firebase-sessions-2.0.0/AndroidManifest.xml:25:13-37
	android:name
		ADDED from [com.google.firebase:firebase-sessions:2.0.0] /Users/<USER>/.gradle/caches/transforms-3/7243a85cae0957d4f2c115b4e3710992/transformed/jetified-firebase-sessions-2.0.0/AndroidManifest.xml:23:13-80
meta-data#com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar
ADDED from [com.google.firebase:firebase-sessions:2.0.0] /Users/<USER>/.gradle/caches/transforms-3/7243a85cae0957d4f2c115b4e3710992/transformed/jetified-firebase-sessions-2.0.0/AndroidManifest.xml:29:13-31:85
	android:value
		ADDED from [com.google.firebase:firebase-sessions:2.0.0] /Users/<USER>/.gradle/caches/transforms-3/7243a85cae0957d4f2c115b4e3710992/transformed/jetified-firebase-sessions-2.0.0/AndroidManifest.xml:31:17-82
	android:name
		ADDED from [com.google.firebase:firebase-sessions:2.0.0] /Users/<USER>/.gradle/caches/transforms-3/7243a85cae0957d4f2c115b4e3710992/transformed/jetified-firebase-sessions-2.0.0/AndroidManifest.xml:30:17-117
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar
ADDED from [com.google.firebase:firebase-firestore:25.1.1] /Users/<USER>/.gradle/caches/transforms-3/530d6bd4293568127cdd25ad75f71303/transformed/jetified-firebase-firestore-25.1.1/AndroidManifest.xml:17:13-19:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:25.1.1] /Users/<USER>/.gradle/caches/transforms-3/530d6bd4293568127cdd25ad75f71303/transformed/jetified-firebase-firestore-25.1.1/AndroidManifest.xml:19:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:25.1.1] /Users/<USER>/.gradle/caches/transforms-3/530d6bd4293568127cdd25ad75f71303/transformed/jetified-firebase-firestore-25.1.1/AndroidManifest.xml:18:17-122
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar
ADDED from [com.google.firebase:firebase-firestore:25.1.1] /Users/<USER>/.gradle/caches/transforms-3/530d6bd4293568127cdd25ad75f71303/transformed/jetified-firebase-firestore-25.1.1/AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:25.1.1] /Users/<USER>/.gradle/caches/transforms-3/530d6bd4293568127cdd25ad75f71303/transformed/jetified-firebase-firestore-25.1.1/AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:25.1.1] /Users/<USER>/.gradle/caches/transforms-3/530d6bd4293568127cdd25ad75f71303/transformed/jetified-firebase-firestore-25.1.1/AndroidManifest.xml:21:17-111
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/63a2baf8c135443c6637edfc9d04aed6/transformed/jetified-play-services-cloud-messaging-17.2.0/AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/transforms-3/63a2baf8c135443c6637edfc9d04aed6/transformed/jetified-play-services-cloud-messaging-17.2.0/AndroidManifest.xml:11:5-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:26:22-79
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:29:9-40:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:31:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:32:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:30:13-78
meta-data#com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED
ADDED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:37:13-39:40
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:39:17-37
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:38:17-92
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:46:9-53:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:49:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:48:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:47:13-82
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:57:13-59:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:59:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:58:17-122
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:60:13-62:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:62:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:61:17-119
meta-data#com.google.firebase.components:com.google.firebase.remoteconfig.FirebaseRemoteConfigKtxRegistrar
ADDED from [com.google.firebase:firebase-config:22.0.1] /Users/<USER>/.gradle/caches/transforms-3/be0c224c4da8bf7baeafb21de3f01ca4/transformed/jetified-firebase-config-22.0.1/AndroidManifest.xml:29:13-31:85
	android:value
		ADDED from [com.google.firebase:firebase-config:22.0.1] /Users/<USER>/.gradle/caches/transforms-3/be0c224c4da8bf7baeafb21de3f01ca4/transformed/jetified-firebase-config-22.0.1/AndroidManifest.xml:31:17-82
	android:name
		ADDED from [com.google.firebase:firebase-config:22.0.1] /Users/<USER>/.gradle/caches/transforms-3/be0c224c4da8bf7baeafb21de3f01ca4/transformed/jetified-firebase-config-22.0.1/AndroidManifest.xml:30:17-128
meta-data#com.google.firebase.components:com.google.firebase.remoteconfig.RemoteConfigRegistrar
ADDED from [com.google.firebase:firebase-config:22.0.1] /Users/<USER>/.gradle/caches/transforms-3/be0c224c4da8bf7baeafb21de3f01ca4/transformed/jetified-firebase-config-22.0.1/AndroidManifest.xml:32:13-34:85
	android:value
		ADDED from [com.google.firebase:firebase-config:22.0.1] /Users/<USER>/.gradle/caches/transforms-3/be0c224c4da8bf7baeafb21de3f01ca4/transformed/jetified-firebase-config-22.0.1/AndroidManifest.xml:34:17-82
	android:name
		ADDED from [com.google.firebase:firebase-config:22.0.1] /Users/<USER>/.gradle/caches/transforms-3/be0c224c4da8bf7baeafb21de3f01ca4/transformed/jetified-firebase-config-22.0.1/AndroidManifest.xml:33:17-117
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/1a290a3f05b797b7236a51bcc1d7f52a/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/1a290a3f05b797b7236a51bcc1d7f52a/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/1a290a3f05b797b7236a51bcc1d7f52a/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/1a290a3f05b797b7236a51bcc1d7f52a/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/1a290a3f05b797b7236a51bcc1d7f52a/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/1a290a3f05b797b7236a51bcc1d7f52a/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/172cbceb397b342c9cf843eb432fc541/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/172cbceb397b342c9cf843eb432fc541/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/172cbceb397b342c9cf843eb432fc541/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:13:17-116
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/transforms-3/43c44d29c9ea2875571e40397afa4dbc/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/transforms-3/43c44d29c9ea2875571e40397afa4dbc/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/transforms-3/43c44d29c9ea2875571e40397afa4dbc/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:26:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/96ffa47d0dd9b7416c80de5663a8865a/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/7309f79d0976d2d00ad66906f83b59f3/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/7309f79d0976d2d00ad66906f83b59f3/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/96ffa47d0dd9b7416c80de5663a8865a/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/96ffa47d0dd9b7416c80de5663a8865a/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/96ffa47d0dd9b7416c80de5663a8865a/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/96ffa47d0dd9b7416c80de5663a8865a/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/96ffa47d0dd9b7416c80de5663a8865a/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:32:17-94
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/87fac691493df4f79988be19c1e693b2/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/87fac691493df4f79988be19c1e693b2/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/87fac691493df4f79988be19c1e693b2/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/87fac691493df4f79988be19c1e693b2/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/87fac691493df4f79988be19c1e693b2/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/87fac691493df4f79988be19c1e693b2/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/87fac691493df4f79988be19c1e693b2/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/87fac691493df4f79988be19c1e693b2/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/87fac691493df4f79988be19c1e693b2/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/87fac691493df4f79988be19c1e693b2/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:34:13-89
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/transforms-3/44f3b8f078250072ad3e855c1c8d37b1/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/transforms-3/44f3b8f078250072ad3e855c1c8d37b1/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/transforms-3/44f3b8f078250072ad3e855c1c8d37b1/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/transforms-3/44f3b8f078250072ad3e855c1c8d37b1/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:20:19-85
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/357784e7a16ccd95c68cfb70e35fa59d/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/357784e7a16ccd95c68cfb70e35fa59d/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/357784e7a16ccd95c68cfb70e35fa59d/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/357784e7a16ccd95c68cfb70e35fa59d/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/357784e7a16ccd95c68cfb70e35fa59d/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/357784e7a16ccd95c68cfb70e35fa59d/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/357784e7a16ccd95c68cfb70e35fa59d/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/357784e7a16ccd95c68cfb70e35fa59d/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/357784e7a16ccd95c68cfb70e35fa59d/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:36:17-109
meta-data#com.google.firebase.components:com.google.firebase.abt.component.AbtRegistrar
ADDED from [com.google.firebase:firebase-abt:21.1.1] /Users/<USER>/.gradle/caches/transforms-3/fed47fe0e911f93eba0838da176bd2db/transformed/jetified-firebase-abt-21.1.1/AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-abt:21.1.1] /Users/<USER>/.gradle/caches/transforms-3/fed47fe0e911f93eba0838da176bd2db/transformed/jetified-firebase-abt-21.1.1/AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-abt:21.1.1] /Users/<USER>/.gradle/caches/transforms-3/fed47fe0e911f93eba0838da176bd2db/transformed/jetified-firebase-abt-21.1.1/AndroidManifest.xml:13:17-109
provider#androidx.startup.InitializationProvider
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/98a13afb1037d30c765555e4ae128327/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/8bc68417b2d3520d1ae45fa0d3b14d45/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/transforms-3/8bc68417b2d3520d1ae45fa0d3b14d45/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/98a13afb1037d30c765555e4ae128327/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/98a13afb1037d30c765555e4ae128327/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/98a13afb1037d30c765555e4ae128327/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/98a13afb1037d30c765555e4ae128327/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:25:13-67
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/98a13afb1037d30c765555e4ae128327/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/98a13afb1037d30c765555e4ae128327/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/98a13afb1037d30c765555e4ae128327/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/1a8b478347169530bac076d1af4efc75/transformed/jetified-window-1.2.0/AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/1a8b478347169530bac076d1af4efc75/transformed/jetified-window-1.2.0/AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/1a8b478347169530bac076d1af4efc75/transformed/jetified-window-1.2.0/AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/1a8b478347169530bac076d1af4efc75/transformed/jetified-window-1.2.0/AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/1a8b478347169530bac076d1af4efc75/transformed/jetified-window-1.2.0/AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/1a8b478347169530bac076d1af4efc75/transformed/jetified-window-1.2.0/AndroidManifest.xml:27:13-51
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/transforms-3/6408c3c41b137332f20e3d3bc0428c8c/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/transforms-3/6408c3c41b137332f20e3d3bc0428c8c/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/transforms-3/6408c3c41b137332f20e3d3bc0428c8c/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:22:13-58
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/a3de342a3f81e3197d28e19f8e01251f/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/a3de342a3f81e3197d28e19f8e01251f/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/a3de342a3f81e3197d28e19f8e01251f/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
permission#com.AlKhabeer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/a3de342a3f81e3197d28e19f8e01251f/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/a3de342a3f81e3197d28e19f8e01251f/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/a3de342a3f81e3197d28e19f8e01251f/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/a3de342a3f81e3197d28e19f8e01251f/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/a3de342a3f81e3197d28e19f8e01251f/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
uses-permission#com.AlKhabeer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/a3de342a3f81e3197d28e19f8e01251f/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/a3de342a3f81e3197d28e19f8e01251f/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/7309f79d0976d2d00ad66906f83b59f3/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/7309f79d0976d2d00ad66906f83b59f3/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/7309f79d0976d2d00ad66906f83b59f3/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/7309f79d0976d2d00ad66906f83b59f3/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/7309f79d0976d2d00ad66906f83b59f3/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/7309f79d0976d2d00ad66906f83b59f3/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/7309f79d0976d2d00ad66906f83b59f3/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:33:13-132
