1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.AlKhabeer"
4    android:versionCode="10"
5    android:versionName="1.0.8" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="34" />
10    <!--
11         <PERSON>lut<PERSON> needs it to communicate with the running application
12         to allow setting breakpoints, to provide hot reload, etc.
13    -->
14    <uses-permission android:name="android.permission.INTERNET" />
14-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:4:5-66
14-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:4:22-64
15    <uses-permission android:name="android.permission.CAMERA" />
15-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:5:5-64
15-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:5:22-62
16    <uses-permission
16-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:6:5-106
17        android:name="android.permission.READ_EXTERNAL_STORAGE"
17-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:6:22-77
18        android:maxSdkVersion="32" />
18-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:6:78-104
19    <uses-permission
19-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:7:5-107
20        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
20-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:7:22-78
21        android:maxSdkVersion="32" />
21-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:7:79-105
22    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
22-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:8:5-76
22-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:8:22-74
23
24    <queries>
24-->[:file_picker] /Users/<USER>/StudioProjects/test/build/file_picker/intermediates/merged_manifest/debug/AndroidManifest.xml:11:5-17:15
25        <intent>
25-->[:file_picker] /Users/<USER>/StudioProjects/test/build/file_picker/intermediates/merged_manifest/debug/AndroidManifest.xml:12:9-16:18
26            <action android:name="android.intent.action.GET_CONTENT" />
26-->[:file_picker] /Users/<USER>/StudioProjects/test/build/file_picker/intermediates/merged_manifest/debug/AndroidManifest.xml:13:13-72
26-->[:file_picker] /Users/<USER>/StudioProjects/test/build/file_picker/intermediates/merged_manifest/debug/AndroidManifest.xml:13:21-69
27
28            <data android:mimeType="*/*" />
28-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:50:17-81
29        </intent>
30    </queries>
31
32    <uses-permission android:name="android.permission.WAKE_LOCK" />
32-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:8:5-68
32-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:8:22-65
33    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- Required by older versions of Google Play services to create IID tokens -->
33-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:9:5-79
33-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:9:22-76
34    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
34-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:26:5-82
34-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:26:22-79
35
36    <permission
36-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/a3de342a3f81e3197d28e19f8e01251f/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
37        android:name="com.AlKhabeer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
37-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/a3de342a3f81e3197d28e19f8e01251f/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
38        android:protectionLevel="signature" />
38-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/a3de342a3f81e3197d28e19f8e01251f/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
39
40    <uses-permission android:name="com.AlKhabeer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
40-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/a3de342a3f81e3197d28e19f8e01251f/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
40-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/a3de342a3f81e3197d28e19f8e01251f/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
41
42    <application
42-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:9:5-64:19
43        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
43-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/a3de342a3f81e3197d28e19f8e01251f/transformed/core-1.13.1/AndroidManifest.xml:28:18-86
44        android:debuggable="true"
45        android:extractNativeLibs="false"
46        android:icon="@mipmap/ic_launcher"
46-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:12:9-43
47        android:label="الخبير  للمزادات"
47-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:10:9-41
48        android:requestLegacyExternalStorage="true" >
48-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:13:9-52
49        <activity
49-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:15:9-53:20
50            android:name="com.AlKhabeer.MainActivity"
50-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:16:13-41
51            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
51-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:20:13-163
52            android:exported="true"
52-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:17:13-36
53            android:hardwareAccelerated="true"
53-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:21:13-47
54            android:launchMode="singleTop"
54-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:18:13-43
55            android:theme="@style/LaunchTheme"
55-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:19:13-47
56            android:windowSoftInputMode="adjustResize" >
56-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:22:13-55
57
58            <!--
59                 Specifies an Android theme to apply to this Activity as soon as
60                 the Android process has started. This theme is visible to the user
61                 while the Flutter UI initializes. After that, this theme continues
62                 to determine the Window background behind the Flutter UI.
63            -->
64            <meta-data
64-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:27:13-30:17
65                android:name="io.flutter.embedding.android.NormalTheme"
65-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:28:15-70
66                android:resource="@style/NormalTheme" />
66-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:29:15-52
67            <!--
68                 Displays an Android View that continues showing the launch screen
69                 Drawable until Flutter paints its first frame, then this splash
70                 screen fades out. A splash screen is useful to avoid any visual
71                 gap between the end of Android's launch screen and the painting of
72                 Flutter's first frame.
73            -->
74            <meta-data
74-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:36:13-39:17
75                android:name="io.flutter.embedding.android.SplashScreenDrawable"
75-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:37:15-79
76                android:resource="@drawable/launch_background" />
76-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:38:15-61
77
78            <intent-filter>
78-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:40:13-43:29
79                <action android:name="android.intent.action.MAIN" />
79-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:41:17-68
79-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:41:25-66
80
81                <category android:name="android.intent.category.LAUNCHER" />
81-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:42:17-76
81-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:42:27-74
82            </intent-filter>
83            <!-- Deep linking -->
84            <meta-data
84-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:45:13-90
85                android:name="flutter_deeplinking_enabled"
85-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:45:24-66
86                android:value="true" />
86-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:45:67-87
87
88            <intent-filter android:autoVerify="true" >
88-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:46:13-51:29
88-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:46:28-53
89                <action android:name="android.intent.action.VIEW" />
89-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:47:17-69
89-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:47:25-66
90
91                <category android:name="android.intent.category.DEFAULT" />
91-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:48:17-76
91-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:48:27-73
92                <category android:name="android.intent.category.BROWSABLE" />
92-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:49:17-78
92-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:49:27-75
93
94                <data
94-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:50:17-81
95                    android:host="alkhabeer.com"
95-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:50:50-78
96                    android:scheme="alkhabeer" />
96-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:50:23-49
97            </intent-filter>
98        </activity>
99
100        <!--
101             Don't delete the meta-data below.
102             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
103        -->
104        <meta-data
104-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:57:9-59:33
105            android:name="flutterEmbedding"
105-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:58:13-44
106            android:value="2" />
106-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:59:13-30
107
108        <service
108-->[:cloud_firestore] /Users/<USER>/StudioProjects/test/build/cloud_firestore/intermediates/merged_manifest/debug/AndroidManifest.xml:8:9-12:19
109            android:name="com.google.firebase.components.ComponentDiscoveryService"
109-->[:cloud_firestore] /Users/<USER>/StudioProjects/test/build/cloud_firestore/intermediates/merged_manifest/debug/AndroidManifest.xml:8:18-89
110            android:directBootAware="true"
110-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/357784e7a16ccd95c68cfb70e35fa59d/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:32:13-43
111            android:exported="false" >
111-->[com.google.firebase:firebase-perf:21.0.2] /Users/<USER>/.gradle/caches/transforms-3/b863a0e80e78acee3b999a288117c346/transformed/jetified-firebase-perf-21.0.2/AndroidManifest.xml:13:13-37
112            <meta-data
112-->[:cloud_firestore] /Users/<USER>/StudioProjects/test/build/cloud_firestore/intermediates/merged_manifest/debug/AndroidManifest.xml:9:13-11:85
113                android:name="com.google.firebase.components:io.flutter.plugins.firebase.firestore.FlutterFirebaseFirestoreRegistrar"
113-->[:cloud_firestore] /Users/<USER>/StudioProjects/test/build/cloud_firestore/intermediates/merged_manifest/debug/AndroidManifest.xml:10:17-134
114                android:value="com.google.firebase.components.ComponentRegistrar" />
114-->[:cloud_firestore] /Users/<USER>/StudioProjects/test/build/cloud_firestore/intermediates/merged_manifest/debug/AndroidManifest.xml:11:17-82
115            <meta-data
115-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:36:13-38:85
116                android:name="com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar"
116-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:37:17-128
117                android:value="com.google.firebase.components.ComponentRegistrar" />
117-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:38:17-82
118            <meta-data
118-->[:firebase_performance] /Users/<USER>/StudioProjects/test/build/firebase_performance/intermediates/merged_manifest/debug/AndroidManifest.xml:11:13-13:85
119                android:name="com.google.firebase.components:io.flutter.plugins.firebase.performance.FlutterFirebaseAppRegistrar"
119-->[:firebase_performance] /Users/<USER>/StudioProjects/test/build/firebase_performance/intermediates/merged_manifest/debug/AndroidManifest.xml:12:17-130
120                android:value="com.google.firebase.components.ComponentRegistrar" />
120-->[:firebase_performance] /Users/<USER>/StudioProjects/test/build/firebase_performance/intermediates/merged_manifest/debug/AndroidManifest.xml:13:17-82
121            <meta-data
121-->[:firebase_core] /Users/<USER>/StudioProjects/test/build/firebase_core/intermediates/merged_manifest/debug/AndroidManifest.xml:9:13-11:85
122                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
122-->[:firebase_core] /Users/<USER>/StudioProjects/test/build/firebase_core/intermediates/merged_manifest/debug/AndroidManifest.xml:10:17-124
123                android:value="com.google.firebase.components.ComponentRegistrar" />
123-->[:firebase_core] /Users/<USER>/StudioProjects/test/build/firebase_core/intermediates/merged_manifest/debug/AndroidManifest.xml:11:17-82
124            <meta-data
124-->[com.google.firebase:firebase-perf:21.0.2] /Users/<USER>/.gradle/caches/transforms-3/b863a0e80e78acee3b999a288117c346/transformed/jetified-firebase-perf-21.0.2/AndroidManifest.xml:14:13-16:85
125                android:name="com.google.firebase.components:com.google.firebase.perf.FirebasePerfKtxRegistrar"
125-->[com.google.firebase:firebase-perf:21.0.2] /Users/<USER>/.gradle/caches/transforms-3/b863a0e80e78acee3b999a288117c346/transformed/jetified-firebase-perf-21.0.2/AndroidManifest.xml:15:17-112
126                android:value="com.google.firebase.components.ComponentRegistrar" />
126-->[com.google.firebase:firebase-perf:21.0.2] /Users/<USER>/.gradle/caches/transforms-3/b863a0e80e78acee3b999a288117c346/transformed/jetified-firebase-perf-21.0.2/AndroidManifest.xml:16:17-82
127            <meta-data
127-->[com.google.firebase:firebase-perf:21.0.2] /Users/<USER>/.gradle/caches/transforms-3/b863a0e80e78acee3b999a288117c346/transformed/jetified-firebase-perf-21.0.2/AndroidManifest.xml:17:13-19:85
128                android:name="com.google.firebase.components:com.google.firebase.perf.FirebasePerfRegistrar"
128-->[com.google.firebase:firebase-perf:21.0.2] /Users/<USER>/.gradle/caches/transforms-3/b863a0e80e78acee3b999a288117c346/transformed/jetified-firebase-perf-21.0.2/AndroidManifest.xml:18:17-109
129                android:value="com.google.firebase.components.ComponentRegistrar" />
129-->[com.google.firebase:firebase-perf:21.0.2] /Users/<USER>/.gradle/caches/transforms-3/b863a0e80e78acee3b999a288117c346/transformed/jetified-firebase-perf-21.0.2/AndroidManifest.xml:19:17-82
130            <meta-data
130-->[com.google.firebase:firebase-sessions:2.0.0] /Users/<USER>/.gradle/caches/transforms-3/7243a85cae0957d4f2c115b4e3710992/transformed/jetified-firebase-sessions-2.0.0/AndroidManifest.xml:29:13-31:85
131                android:name="com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar"
131-->[com.google.firebase:firebase-sessions:2.0.0] /Users/<USER>/.gradle/caches/transforms-3/7243a85cae0957d4f2c115b4e3710992/transformed/jetified-firebase-sessions-2.0.0/AndroidManifest.xml:30:17-117
132                android:value="com.google.firebase.components.ComponentRegistrar" />
132-->[com.google.firebase:firebase-sessions:2.0.0] /Users/<USER>/.gradle/caches/transforms-3/7243a85cae0957d4f2c115b4e3710992/transformed/jetified-firebase-sessions-2.0.0/AndroidManifest.xml:31:17-82
133            <meta-data
133-->[com.google.firebase:firebase-firestore:25.1.1] /Users/<USER>/.gradle/caches/transforms-3/530d6bd4293568127cdd25ad75f71303/transformed/jetified-firebase-firestore-25.1.1/AndroidManifest.xml:17:13-19:85
134                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
134-->[com.google.firebase:firebase-firestore:25.1.1] /Users/<USER>/.gradle/caches/transforms-3/530d6bd4293568127cdd25ad75f71303/transformed/jetified-firebase-firestore-25.1.1/AndroidManifest.xml:18:17-122
135                android:value="com.google.firebase.components.ComponentRegistrar" />
135-->[com.google.firebase:firebase-firestore:25.1.1] /Users/<USER>/.gradle/caches/transforms-3/530d6bd4293568127cdd25ad75f71303/transformed/jetified-firebase-firestore-25.1.1/AndroidManifest.xml:19:17-82
136            <meta-data
136-->[com.google.firebase:firebase-firestore:25.1.1] /Users/<USER>/.gradle/caches/transforms-3/530d6bd4293568127cdd25ad75f71303/transformed/jetified-firebase-firestore-25.1.1/AndroidManifest.xml:20:13-22:85
137                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
137-->[com.google.firebase:firebase-firestore:25.1.1] /Users/<USER>/.gradle/caches/transforms-3/530d6bd4293568127cdd25ad75f71303/transformed/jetified-firebase-firestore-25.1.1/AndroidManifest.xml:21:17-111
138                android:value="com.google.firebase.components.ComponentRegistrar" />
138-->[com.google.firebase:firebase-firestore:25.1.1] /Users/<USER>/.gradle/caches/transforms-3/530d6bd4293568127cdd25ad75f71303/transformed/jetified-firebase-firestore-25.1.1/AndroidManifest.xml:22:17-82
139            <meta-data
139-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:57:13-59:85
140                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
140-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:58:17-122
141                android:value="com.google.firebase.components.ComponentRegistrar" />
141-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:59:17-82
142            <meta-data
142-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:60:13-62:85
143                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
143-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:61:17-119
144                android:value="com.google.firebase.components.ComponentRegistrar" />
144-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:62:17-82
145            <meta-data
145-->[com.google.firebase:firebase-config:22.0.1] /Users/<USER>/.gradle/caches/transforms-3/be0c224c4da8bf7baeafb21de3f01ca4/transformed/jetified-firebase-config-22.0.1/AndroidManifest.xml:29:13-31:85
146                android:name="com.google.firebase.components:com.google.firebase.remoteconfig.FirebaseRemoteConfigKtxRegistrar"
146-->[com.google.firebase:firebase-config:22.0.1] /Users/<USER>/.gradle/caches/transforms-3/be0c224c4da8bf7baeafb21de3f01ca4/transformed/jetified-firebase-config-22.0.1/AndroidManifest.xml:30:17-128
147                android:value="com.google.firebase.components.ComponentRegistrar" />
147-->[com.google.firebase:firebase-config:22.0.1] /Users/<USER>/.gradle/caches/transforms-3/be0c224c4da8bf7baeafb21de3f01ca4/transformed/jetified-firebase-config-22.0.1/AndroidManifest.xml:31:17-82
148            <meta-data
148-->[com.google.firebase:firebase-config:22.0.1] /Users/<USER>/.gradle/caches/transforms-3/be0c224c4da8bf7baeafb21de3f01ca4/transformed/jetified-firebase-config-22.0.1/AndroidManifest.xml:32:13-34:85
149                android:name="com.google.firebase.components:com.google.firebase.remoteconfig.RemoteConfigRegistrar"
149-->[com.google.firebase:firebase-config:22.0.1] /Users/<USER>/.gradle/caches/transforms-3/be0c224c4da8bf7baeafb21de3f01ca4/transformed/jetified-firebase-config-22.0.1/AndroidManifest.xml:33:17-117
150                android:value="com.google.firebase.components.ComponentRegistrar" />
150-->[com.google.firebase:firebase-config:22.0.1] /Users/<USER>/.gradle/caches/transforms-3/be0c224c4da8bf7baeafb21de3f01ca4/transformed/jetified-firebase-config-22.0.1/AndroidManifest.xml:34:17-82
151            <meta-data
151-->[com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/1a290a3f05b797b7236a51bcc1d7f52a/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:15:13-17:85
152                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
152-->[com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/1a290a3f05b797b7236a51bcc1d7f52a/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:16:17-130
153                android:value="com.google.firebase.components.ComponentRegistrar" />
153-->[com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/1a290a3f05b797b7236a51bcc1d7f52a/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:17:17-82
154            <meta-data
154-->[com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/1a290a3f05b797b7236a51bcc1d7f52a/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:18:13-20:85
155                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
155-->[com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/1a290a3f05b797b7236a51bcc1d7f52a/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:19:17-127
156                android:value="com.google.firebase.components.ComponentRegistrar" />
156-->[com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/1a290a3f05b797b7236a51bcc1d7f52a/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:20:17-82
157            <meta-data
157-->[com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/172cbceb397b342c9cf843eb432fc541/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:12:13-14:85
158                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
158-->[com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/172cbceb397b342c9cf843eb432fc541/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:13:17-116
159                android:value="com.google.firebase.components.ComponentRegistrar" />
159-->[com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/172cbceb397b342c9cf843eb432fc541/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:14:17-82
160            <meta-data
160-->[com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/transforms-3/43c44d29c9ea2875571e40397afa4dbc/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:25:13-27:85
161                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
161-->[com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/transforms-3/43c44d29c9ea2875571e40397afa4dbc/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:26:17-115
162                android:value="com.google.firebase.components.ComponentRegistrar" />
162-->[com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/transforms-3/43c44d29c9ea2875571e40397afa4dbc/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:27:17-82
163            <meta-data
163-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/357784e7a16ccd95c68cfb70e35fa59d/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:35:13-37:85
164                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
164-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/357784e7a16ccd95c68cfb70e35fa59d/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:36:17-109
165                android:value="com.google.firebase.components.ComponentRegistrar" />
165-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/357784e7a16ccd95c68cfb70e35fa59d/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:37:17-82
166            <meta-data
166-->[com.google.firebase:firebase-abt:21.1.1] /Users/<USER>/.gradle/caches/transforms-3/fed47fe0e911f93eba0838da176bd2db/transformed/jetified-firebase-abt-21.1.1/AndroidManifest.xml:12:13-14:85
167                android:name="com.google.firebase.components:com.google.firebase.abt.component.AbtRegistrar"
167-->[com.google.firebase:firebase-abt:21.1.1] /Users/<USER>/.gradle/caches/transforms-3/fed47fe0e911f93eba0838da176bd2db/transformed/jetified-firebase-abt-21.1.1/AndroidManifest.xml:13:17-109
168                android:value="com.google.firebase.components.ComponentRegistrar" />
168-->[com.google.firebase:firebase-abt:21.1.1] /Users/<USER>/.gradle/caches/transforms-3/fed47fe0e911f93eba0838da176bd2db/transformed/jetified-firebase-abt-21.1.1/AndroidManifest.xml:14:17-82
169        </service>
170        <service
170-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:14:9-17:72
171            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService"
171-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:15:13-107
172            android:exported="false"
172-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:16:13-37
173            android:permission="android.permission.BIND_JOB_SERVICE" />
173-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:17:13-69
174        <service
174-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:18:9-24:19
175            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
175-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:19:13-97
176            android:exported="false" >
176-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:20:13-37
177            <intent-filter>
177-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:21:13-23:29
178                <action android:name="com.google.firebase.MESSAGING_EVENT" />
178-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:22:17-78
178-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:22:25-75
179            </intent-filter>
180        </service>
181
182        <receiver
182-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:26:9-33:20
183            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
183-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:27:13-98
184            android:exported="true"
184-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:28:13-36
185            android:permission="com.google.android.c2dm.permission.SEND" >
185-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:29:13-73
186            <intent-filter>
186-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:30:13-32:29
187                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
187-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:31:17-81
187-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:31:25-78
188            </intent-filter>
189        </receiver>
190
191        <provider
191-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:41:9-45:38
192            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider"
192-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:42:13-102
193            android:authorities="com.AlKhabeer.flutterfirebasemessaginginitprovider"
193-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:43:13-88
194            android:exported="false"
194-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:44:13-37
195            android:initOrder="99" />
195-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:45:13-35
196        <provider
196-->[:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:9:9-17:20
197            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
197-->[:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:10:13-82
198            android:authorities="com.AlKhabeer.flutter.image_provider"
198-->[:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:11:13-74
199            android:exported="false"
199-->[:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:12:13-37
200            android:grantUriPermissions="true" >
200-->[:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:13:13-47
201            <meta-data
201-->[:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:14:13-16:75
202                android:name="android.support.FILE_PROVIDER_PATHS"
202-->[:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:15:17-67
203                android:resource="@xml/flutter_image_picker_file_paths" />
203-->[:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:16:17-72
204        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
205        <service
205-->[:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:19:9-31:19
206            android:name="com.google.android.gms.metadata.ModuleDependencies"
206-->[:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:20:13-78
207            android:enabled="false"
207-->[:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:21:13-36
208            android:exported="false" >
208-->[:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:22:13-37
209            <intent-filter>
209-->[:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:24:13-26:29
210                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
210-->[:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:25:17-94
210-->[:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:25:25-91
211            </intent-filter>
212
213            <meta-data
213-->[:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:28:13-30:36
214                android:name="photopicker_activity:0:required"
214-->[:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:29:17-63
215                android:value="" />
215-->[:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:30:17-33
216        </service>
217
218        <activity
218-->[:url_launcher_android] /Users/<USER>/StudioProjects/test/build/url_launcher_android/intermediates/merged_manifest/debug/AndroidManifest.xml:8:9-11:74
219            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
219-->[:url_launcher_android] /Users/<USER>/StudioProjects/test/build/url_launcher_android/intermediates/merged_manifest/debug/AndroidManifest.xml:9:13-74
220            android:exported="false"
220-->[:url_launcher_android] /Users/<USER>/StudioProjects/test/build/url_launcher_android/intermediates/merged_manifest/debug/AndroidManifest.xml:10:13-37
221            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
221-->[:url_launcher_android] /Users/<USER>/StudioProjects/test/build/url_launcher_android/intermediates/merged_manifest/debug/AndroidManifest.xml:11:13-71
222
223        <service
223-->[com.google.firebase:firebase-sessions:2.0.0] /Users/<USER>/.gradle/caches/transforms-3/7243a85cae0957d4f2c115b4e3710992/transformed/jetified-firebase-sessions-2.0.0/AndroidManifest.xml:22:9-25:40
224            android:name="com.google.firebase.sessions.SessionLifecycleService"
224-->[com.google.firebase:firebase-sessions:2.0.0] /Users/<USER>/.gradle/caches/transforms-3/7243a85cae0957d4f2c115b4e3710992/transformed/jetified-firebase-sessions-2.0.0/AndroidManifest.xml:23:13-80
225            android:enabled="true"
225-->[com.google.firebase:firebase-sessions:2.0.0] /Users/<USER>/.gradle/caches/transforms-3/7243a85cae0957d4f2c115b4e3710992/transformed/jetified-firebase-sessions-2.0.0/AndroidManifest.xml:24:13-35
226            android:exported="false" />
226-->[com.google.firebase:firebase-sessions:2.0.0] /Users/<USER>/.gradle/caches/transforms-3/7243a85cae0957d4f2c115b4e3710992/transformed/jetified-firebase-sessions-2.0.0/AndroidManifest.xml:25:13-37
227
228        <receiver
228-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:29:9-40:20
229            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
229-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:30:13-78
230            android:exported="true"
230-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:31:13-36
231            android:permission="com.google.android.c2dm.permission.SEND" >
231-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:32:13-73
232            <intent-filter>
232-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:30:13-32:29
233                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
233-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:31:17-81
233-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:31:25-78
234            </intent-filter>
235
236            <meta-data
236-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:37:13-39:40
237                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
237-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:38:17-92
238                android:value="true" />
238-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:39:17-37
239        </receiver>
240        <!--
241             FirebaseMessagingService performs security checks at runtime,
242             but set to not exported to explicitly avoid allowing another app to call it.
243        -->
244        <service
244-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:46:9-53:19
245            android:name="com.google.firebase.messaging.FirebaseMessagingService"
245-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:47:13-82
246            android:directBootAware="true"
246-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:48:13-43
247            android:exported="false" >
247-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:49:13-37
248            <intent-filter android:priority="-500" >
248-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:21:13-23:29
249                <action android:name="com.google.firebase.MESSAGING_EVENT" />
249-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:22:17-78
249-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:22:25-75
250            </intent-filter>
251        </service>
252        <service
252-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/96ffa47d0dd9b7416c80de5663a8865a/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:28:9-34:19
253            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
253-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/96ffa47d0dd9b7416c80de5663a8865a/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:29:13-103
254            android:exported="false" >
254-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/96ffa47d0dd9b7416c80de5663a8865a/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:30:13-37
255            <meta-data
255-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/96ffa47d0dd9b7416c80de5663a8865a/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:31:13-33:39
256                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
256-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/96ffa47d0dd9b7416c80de5663a8865a/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:32:17-94
257                android:value="cct" />
257-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/96ffa47d0dd9b7416c80de5663a8865a/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:33:17-36
258        </service>
259
260        <activity
260-->[com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/87fac691493df4f79988be19c1e693b2/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:23:9-27:75
261            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
261-->[com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/87fac691493df4f79988be19c1e693b2/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:24:13-93
262            android:excludeFromRecents="true"
262-->[com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/87fac691493df4f79988be19c1e693b2/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:25:13-46
263            android:exported="false"
263-->[com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/87fac691493df4f79988be19c1e693b2/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:26:13-37
264            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
264-->[com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/87fac691493df4f79988be19c1e693b2/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:27:13-72
265        <!--
266            Service handling Google Sign-In user revocation. For apps that do not integrate with
267            Google Sign-In, this service will never be started.
268        -->
269        <service
269-->[com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/87fac691493df4f79988be19c1e693b2/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:33:9-37:51
270            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
270-->[com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/87fac691493df4f79988be19c1e693b2/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:34:13-89
271            android:exported="true"
271-->[com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/87fac691493df4f79988be19c1e693b2/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:35:13-36
272            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
272-->[com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/87fac691493df4f79988be19c1e693b2/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:36:13-107
273            android:visibleToInstantApps="true" />
273-->[com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/87fac691493df4f79988be19c1e693b2/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:37:13-48
274
275        <activity
275-->[com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/transforms-3/44f3b8f078250072ad3e855c1c8d37b1/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:20:9-22:45
276            android:name="com.google.android.gms.common.api.GoogleApiActivity"
276-->[com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/transforms-3/44f3b8f078250072ad3e855c1c8d37b1/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:20:19-85
277            android:exported="false"
277-->[com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/transforms-3/44f3b8f078250072ad3e855c1c8d37b1/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:22:19-43
278            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
278-->[com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/transforms-3/44f3b8f078250072ad3e855c1c8d37b1/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:21:19-78
279
280        <provider
280-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/357784e7a16ccd95c68cfb70e35fa59d/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:23:9-28:39
281            android:name="com.google.firebase.provider.FirebaseInitProvider"
281-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/357784e7a16ccd95c68cfb70e35fa59d/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:24:13-77
282            android:authorities="com.AlKhabeer.firebaseinitprovider"
282-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/357784e7a16ccd95c68cfb70e35fa59d/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:25:13-72
283            android:directBootAware="true"
283-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/357784e7a16ccd95c68cfb70e35fa59d/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:26:13-43
284            android:exported="false"
284-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/357784e7a16ccd95c68cfb70e35fa59d/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:27:13-37
285            android:initOrder="100" />
285-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/357784e7a16ccd95c68cfb70e35fa59d/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:28:13-36
286        <provider
286-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/98a13afb1037d30c765555e4ae128327/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:24:9-32:20
287            android:name="androidx.startup.InitializationProvider"
287-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/98a13afb1037d30c765555e4ae128327/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:25:13-67
288            android:authorities="com.AlKhabeer.androidx-startup"
288-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/98a13afb1037d30c765555e4ae128327/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:26:13-68
289            android:exported="false" >
289-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/98a13afb1037d30c765555e4ae128327/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:27:13-37
290            <meta-data
290-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/98a13afb1037d30c765555e4ae128327/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
291                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
291-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/98a13afb1037d30c765555e4ae128327/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
292                android:value="androidx.startup" />
292-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/98a13afb1037d30c765555e4ae128327/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
293            <meta-data
293-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
294                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
294-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
295                android:value="androidx.startup" />
295-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
296        </provider>
297
298        <uses-library
298-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/1a8b478347169530bac076d1af4efc75/transformed/jetified-window-1.2.0/AndroidManifest.xml:23:9-25:40
299            android:name="androidx.window.extensions"
299-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/1a8b478347169530bac076d1af4efc75/transformed/jetified-window-1.2.0/AndroidManifest.xml:24:13-54
300            android:required="false" />
300-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/1a8b478347169530bac076d1af4efc75/transformed/jetified-window-1.2.0/AndroidManifest.xml:25:13-37
301        <uses-library
301-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/1a8b478347169530bac076d1af4efc75/transformed/jetified-window-1.2.0/AndroidManifest.xml:26:9-28:40
302            android:name="androidx.window.sidecar"
302-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/1a8b478347169530bac076d1af4efc75/transformed/jetified-window-1.2.0/AndroidManifest.xml:27:13-51
303            android:required="false" />
303-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/1a8b478347169530bac076d1af4efc75/transformed/jetified-window-1.2.0/AndroidManifest.xml:28:13-37
304
305        <meta-data
305-->[com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/transforms-3/6408c3c41b137332f20e3d3bc0428c8c/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:21:9-23:69
306            android:name="com.google.android.gms.version"
306-->[com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/transforms-3/6408c3c41b137332f20e3d3bc0428c8c/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:22:13-58
307            android:value="@integer/google_play_services_version" />
307-->[com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/transforms-3/6408c3c41b137332f20e3d3bc0428c8c/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:23:13-66
308
309        <receiver
309-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
310            android:name="androidx.profileinstaller.ProfileInstallReceiver"
310-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
311            android:directBootAware="false"
311-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
312            android:enabled="true"
312-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
313            android:exported="true"
313-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
314            android:permission="android.permission.DUMP" >
314-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
315            <intent-filter>
315-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
316                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
316-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
316-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
317            </intent-filter>
318            <intent-filter>
318-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
319                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
319-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
319-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
320            </intent-filter>
321            <intent-filter>
321-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
322                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
322-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
322-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
323            </intent-filter>
324            <intent-filter>
324-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
325                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
325-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
325-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
326            </intent-filter>
327        </receiver>
328
329        <service
329-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/7309f79d0976d2d00ad66906f83b59f3/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:26:9-30:19
330            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
330-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/7309f79d0976d2d00ad66906f83b59f3/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:27:13-117
331            android:exported="false"
331-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/7309f79d0976d2d00ad66906f83b59f3/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:28:13-37
332            android:permission="android.permission.BIND_JOB_SERVICE" >
332-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/7309f79d0976d2d00ad66906f83b59f3/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:29:13-69
333        </service>
334
335        <receiver
335-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/7309f79d0976d2d00ad66906f83b59f3/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:32:9-34:40
336            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
336-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/7309f79d0976d2d00ad66906f83b59f3/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:33:13-132
337            android:exported="false" />
337-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/7309f79d0976d2d00ad66906f83b59f3/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:34:13-37
338    </application>
339
340</manifest>
