1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.AlKhabeer"
4    android:versionCode="10"
5    android:versionName="1.0.8" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="34" />
10    <!--
11         <PERSON>lut<PERSON> needs it to communicate with the running application
12         to allow setting breakpoints, to provide hot reload, etc.
13    -->
14    <uses-permission android:name="android.permission.INTERNET" />
14-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:4:5-66
14-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:4:22-64
15    <uses-permission android:name="android.permission.CAMERA" />
15-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:5:5-64
15-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:5:22-62
16    <uses-permission
16-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:6:5-106
17        android:name="android.permission.READ_EXTERNAL_STORAGE"
17-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:6:22-77
18        android:maxSdkVersion="32" />
18-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:6:78-104
19    <uses-permission
19-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:7:5-107
20        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
20-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:7:22-78
21        android:maxSdkVersion="32" />
21-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:7:79-105
22    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
22-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:8:5-76
22-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:8:22-74
23
24    <queries>
24-->[:file_picker] /Users/<USER>/StudioProjects/test/build/file_picker/intermediates/merged_manifest/debug/AndroidManifest.xml:11:5-17:15
25        <intent>
25-->[:file_picker] /Users/<USER>/StudioProjects/test/build/file_picker/intermediates/merged_manifest/debug/AndroidManifest.xml:12:9-16:18
26            <action android:name="android.intent.action.GET_CONTENT" />
26-->[:file_picker] /Users/<USER>/StudioProjects/test/build/file_picker/intermediates/merged_manifest/debug/AndroidManifest.xml:13:13-72
26-->[:file_picker] /Users/<USER>/StudioProjects/test/build/file_picker/intermediates/merged_manifest/debug/AndroidManifest.xml:13:21-69
27
28            <data android:mimeType="*/*" />
28-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:50:17-81
29        </intent>
30    </queries>
31
32    <uses-permission android:name="android.permission.WAKE_LOCK" />
32-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:8:5-68
32-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:8:22-65
33    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- Required by older versions of Google Play services to create IID tokens -->
33-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:9:5-79
33-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:9:22-76
34    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
34-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:26:5-82
34-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:26:22-79
35
36    <permission
36-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/a3de342a3f81e3197d28e19f8e01251f/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
37        android:name="com.AlKhabeer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
37-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/a3de342a3f81e3197d28e19f8e01251f/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
38        android:protectionLevel="signature" />
38-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/a3de342a3f81e3197d28e19f8e01251f/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
39
40    <uses-permission android:name="com.AlKhabeer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
40-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/a3de342a3f81e3197d28e19f8e01251f/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
40-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/a3de342a3f81e3197d28e19f8e01251f/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
41
42    <application
42-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:9:5-64:19
43        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
43-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/transforms-3/a3de342a3f81e3197d28e19f8e01251f/transformed/core-1.13.1/AndroidManifest.xml:28:18-86
44        android:debuggable="true"
45        android:extractNativeLibs="false"
46        android:icon="@mipmap/ic_launcher"
46-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:12:9-43
47        android:label="الخبير  للمزادات"
47-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:10:9-41
48        android:requestLegacyExternalStorage="true" >
48-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:13:9-52
49        <activity
49-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:15:9-53:20
50            android:name="com.AlKhabeer.MainActivity"
50-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:16:13-41
51            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
51-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:20:13-163
52            android:exported="true"
52-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:17:13-36
53            android:hardwareAccelerated="true"
53-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:21:13-47
54            android:launchMode="singleTop"
54-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:18:13-43
55            android:theme="@style/LaunchTheme"
55-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:19:13-47
56            android:windowSoftInputMode="adjustResize" >
56-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:22:13-55
57
58            <!--
59                 Specifies an Android theme to apply to this Activity as soon as
60                 the Android process has started. This theme is visible to the user
61                 while the Flutter UI initializes. After that, this theme continues
62                 to determine the Window background behind the Flutter UI.
63            -->
64            <meta-data
64-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:27:13-30:17
65                android:name="io.flutter.embedding.android.NormalTheme"
65-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:28:15-70
66                android:resource="@style/NormalTheme" />
66-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:29:15-52
67            <!--
68                 Displays an Android View that continues showing the launch screen
69                 Drawable until Flutter paints its first frame, then this splash
70                 screen fades out. A splash screen is useful to avoid any visual
71                 gap between the end of Android's launch screen and the painting of
72                 Flutter's first frame.
73            -->
74            <meta-data
74-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:36:13-39:17
75                android:name="io.flutter.embedding.android.SplashScreenDrawable"
75-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:37:15-79
76                android:resource="@drawable/launch_background" />
76-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:38:15-61
77
78            <intent-filter>
78-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:40:13-43:29
79                <action android:name="android.intent.action.MAIN" />
79-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:41:17-68
79-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:41:25-66
80
81                <category android:name="android.intent.category.LAUNCHER" />
81-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:42:17-76
81-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:42:27-74
82            </intent-filter>
83            <!-- Deep linking -->
84            <meta-data
84-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:45:13-90
85                android:name="flutter_deeplinking_enabled"
85-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:45:24-66
86                android:value="true" />
86-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:45:67-87
87
88            <intent-filter android:autoVerify="true" >
88-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:46:13-51:29
88-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:46:28-53
89                <action android:name="android.intent.action.VIEW" />
89-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:47:17-69
89-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:47:25-66
90
91                <category android:name="android.intent.category.DEFAULT" />
91-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:48:17-76
91-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:48:27-73
92                <category android:name="android.intent.category.BROWSABLE" />
92-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:49:17-78
92-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:49:27-75
93
94                <data
94-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:50:17-81
95                    android:host="alkhabeer.com"
95-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:50:50-78
96                    android:scheme="alkhabeer" />
96-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:50:23-49
97            </intent-filter>
98        </activity>
99
100        <!--
101             Don't delete the meta-data below.
102             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
103        -->
104        <meta-data
104-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:57:9-59:33
105            android:name="flutterEmbedding"
105-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:58:13-44
106            android:value="2" />
106-->/Users/<USER>/StudioProjects/test/android/app/src/main/AndroidManifest.xml:59:13-30
107
108        <service
108-->[:cloud_firestore] /Users/<USER>/StudioProjects/test/build/cloud_firestore/intermediates/merged_manifest/debug/AndroidManifest.xml:8:9-12:19
109            android:name="com.google.firebase.components.ComponentDiscoveryService"
109-->[:cloud_firestore] /Users/<USER>/StudioProjects/test/build/cloud_firestore/intermediates/merged_manifest/debug/AndroidManifest.xml:8:18-89
110            android:directBootAware="true"
110-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/357784e7a16ccd95c68cfb70e35fa59d/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:32:13-43
111            android:exported="false" >
111-->[com.google.firebase:firebase-firestore:25.1.1] /Users/<USER>/.gradle/caches/transforms-3/530d6bd4293568127cdd25ad75f71303/transformed/jetified-firebase-firestore-25.1.1/AndroidManifest.xml:16:13-37
112            <meta-data
112-->[:cloud_firestore] /Users/<USER>/StudioProjects/test/build/cloud_firestore/intermediates/merged_manifest/debug/AndroidManifest.xml:9:13-11:85
113                android:name="com.google.firebase.components:io.flutter.plugins.firebase.firestore.FlutterFirebaseFirestoreRegistrar"
113-->[:cloud_firestore] /Users/<USER>/StudioProjects/test/build/cloud_firestore/intermediates/merged_manifest/debug/AndroidManifest.xml:10:17-134
114                android:value="com.google.firebase.components.ComponentRegistrar" />
114-->[:cloud_firestore] /Users/<USER>/StudioProjects/test/build/cloud_firestore/intermediates/merged_manifest/debug/AndroidManifest.xml:11:17-82
115            <meta-data
115-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:36:13-38:85
116                android:name="com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar"
116-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:37:17-128
117                android:value="com.google.firebase.components.ComponentRegistrar" />
117-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:38:17-82
118            <meta-data
118-->[:firebase_core] /Users/<USER>/StudioProjects/test/build/firebase_core/intermediates/merged_manifest/debug/AndroidManifest.xml:9:13-11:85
119                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
119-->[:firebase_core] /Users/<USER>/StudioProjects/test/build/firebase_core/intermediates/merged_manifest/debug/AndroidManifest.xml:10:17-124
120                android:value="com.google.firebase.components.ComponentRegistrar" />
120-->[:firebase_core] /Users/<USER>/StudioProjects/test/build/firebase_core/intermediates/merged_manifest/debug/AndroidManifest.xml:11:17-82
121            <meta-data
121-->[com.google.firebase:firebase-firestore:25.1.1] /Users/<USER>/.gradle/caches/transforms-3/530d6bd4293568127cdd25ad75f71303/transformed/jetified-firebase-firestore-25.1.1/AndroidManifest.xml:17:13-19:85
122                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
122-->[com.google.firebase:firebase-firestore:25.1.1] /Users/<USER>/.gradle/caches/transforms-3/530d6bd4293568127cdd25ad75f71303/transformed/jetified-firebase-firestore-25.1.1/AndroidManifest.xml:18:17-122
123                android:value="com.google.firebase.components.ComponentRegistrar" />
123-->[com.google.firebase:firebase-firestore:25.1.1] /Users/<USER>/.gradle/caches/transforms-3/530d6bd4293568127cdd25ad75f71303/transformed/jetified-firebase-firestore-25.1.1/AndroidManifest.xml:19:17-82
124            <meta-data
124-->[com.google.firebase:firebase-firestore:25.1.1] /Users/<USER>/.gradle/caches/transforms-3/530d6bd4293568127cdd25ad75f71303/transformed/jetified-firebase-firestore-25.1.1/AndroidManifest.xml:20:13-22:85
125                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
125-->[com.google.firebase:firebase-firestore:25.1.1] /Users/<USER>/.gradle/caches/transforms-3/530d6bd4293568127cdd25ad75f71303/transformed/jetified-firebase-firestore-25.1.1/AndroidManifest.xml:21:17-111
126                android:value="com.google.firebase.components.ComponentRegistrar" />
126-->[com.google.firebase:firebase-firestore:25.1.1] /Users/<USER>/.gradle/caches/transforms-3/530d6bd4293568127cdd25ad75f71303/transformed/jetified-firebase-firestore-25.1.1/AndroidManifest.xml:22:17-82
127            <meta-data
127-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:57:13-59:85
128                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
128-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:58:17-122
129                android:value="com.google.firebase.components.ComponentRegistrar" />
129-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:59:17-82
130            <meta-data
130-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:60:13-62:85
131                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
131-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:61:17-119
132                android:value="com.google.firebase.components.ComponentRegistrar" />
132-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:62:17-82
133            <meta-data
133-->[com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/1a290a3f05b797b7236a51bcc1d7f52a/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:15:13-17:85
134                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
134-->[com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/1a290a3f05b797b7236a51bcc1d7f52a/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:16:17-130
135                android:value="com.google.firebase.components.ComponentRegistrar" />
135-->[com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/1a290a3f05b797b7236a51bcc1d7f52a/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:17:17-82
136            <meta-data
136-->[com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/1a290a3f05b797b7236a51bcc1d7f52a/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:18:13-20:85
137                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
137-->[com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/1a290a3f05b797b7236a51bcc1d7f52a/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:19:17-127
138                android:value="com.google.firebase.components.ComponentRegistrar" />
138-->[com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/transforms-3/1a290a3f05b797b7236a51bcc1d7f52a/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:20:17-82
139            <meta-data
139-->[com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/172cbceb397b342c9cf843eb432fc541/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:12:13-14:85
140                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
140-->[com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/172cbceb397b342c9cf843eb432fc541/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:13:17-116
141                android:value="com.google.firebase.components.ComponentRegistrar" />
141-->[com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/172cbceb397b342c9cf843eb432fc541/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:14:17-82
142            <meta-data
142-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/357784e7a16ccd95c68cfb70e35fa59d/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:35:13-37:85
143                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
143-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/357784e7a16ccd95c68cfb70e35fa59d/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:36:17-109
144                android:value="com.google.firebase.components.ComponentRegistrar" />
144-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/357784e7a16ccd95c68cfb70e35fa59d/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:37:17-82
145            <meta-data
145-->[com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/transforms-3/43c44d29c9ea2875571e40397afa4dbc/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:25:13-27:85
146                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
146-->[com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/transforms-3/43c44d29c9ea2875571e40397afa4dbc/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:26:17-115
147                android:value="com.google.firebase.components.ComponentRegistrar" />
147-->[com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/transforms-3/43c44d29c9ea2875571e40397afa4dbc/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:27:17-82
148        </service>
149        <service
149-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:14:9-17:72
150            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService"
150-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:15:13-107
151            android:exported="false"
151-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:16:13-37
152            android:permission="android.permission.BIND_JOB_SERVICE" />
152-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:17:13-69
153        <service
153-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:18:9-24:19
154            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
154-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:19:13-97
155            android:exported="false" >
155-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:20:13-37
156            <intent-filter>
156-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:21:13-23:29
157                <action android:name="com.google.firebase.MESSAGING_EVENT" />
157-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:22:17-78
157-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:22:25-75
158            </intent-filter>
159        </service>
160
161        <receiver
161-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:26:9-33:20
162            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
162-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:27:13-98
163            android:exported="true"
163-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:28:13-36
164            android:permission="com.google.android.c2dm.permission.SEND" >
164-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:29:13-73
165            <intent-filter>
165-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:30:13-32:29
166                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
166-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:31:17-81
166-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:31:25-78
167            </intent-filter>
168        </receiver>
169
170        <provider
170-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:41:9-45:38
171            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider"
171-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:42:13-102
172            android:authorities="com.AlKhabeer.flutterfirebasemessaginginitprovider"
172-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:43:13-88
173            android:exported="false"
173-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:44:13-37
174            android:initOrder="99" />
174-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:45:13-35
175        <provider
175-->[:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:9:9-17:20
176            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
176-->[:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:10:13-82
177            android:authorities="com.AlKhabeer.flutter.image_provider"
177-->[:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:11:13-74
178            android:exported="false"
178-->[:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:12:13-37
179            android:grantUriPermissions="true" >
179-->[:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:13:13-47
180            <meta-data
180-->[:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:14:13-16:75
181                android:name="android.support.FILE_PROVIDER_PATHS"
181-->[:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:15:17-67
182                android:resource="@xml/flutter_image_picker_file_paths" />
182-->[:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:16:17-72
183        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
184        <service
184-->[:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:19:9-31:19
185            android:name="com.google.android.gms.metadata.ModuleDependencies"
185-->[:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:20:13-78
186            android:enabled="false"
186-->[:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:21:13-36
187            android:exported="false" >
187-->[:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:22:13-37
188            <intent-filter>
188-->[:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:24:13-26:29
189                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
189-->[:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:25:17-94
189-->[:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:25:25-91
190            </intent-filter>
191
192            <meta-data
192-->[:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:28:13-30:36
193                android:name="photopicker_activity:0:required"
193-->[:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:29:17-63
194                android:value="" />
194-->[:image_picker_android] /Users/<USER>/StudioProjects/test/build/image_picker_android/intermediates/merged_manifest/debug/AndroidManifest.xml:30:17-33
195        </service>
196
197        <activity
197-->[:url_launcher_android] /Users/<USER>/StudioProjects/test/build/url_launcher_android/intermediates/merged_manifest/debug/AndroidManifest.xml:8:9-11:74
198            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
198-->[:url_launcher_android] /Users/<USER>/StudioProjects/test/build/url_launcher_android/intermediates/merged_manifest/debug/AndroidManifest.xml:9:13-74
199            android:exported="false"
199-->[:url_launcher_android] /Users/<USER>/StudioProjects/test/build/url_launcher_android/intermediates/merged_manifest/debug/AndroidManifest.xml:10:13-37
200            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
200-->[:url_launcher_android] /Users/<USER>/StudioProjects/test/build/url_launcher_android/intermediates/merged_manifest/debug/AndroidManifest.xml:11:13-71
201
202        <receiver
202-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:29:9-40:20
203            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
203-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:30:13-78
204            android:exported="true"
204-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:31:13-36
205            android:permission="com.google.android.c2dm.permission.SEND" >
205-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:32:13-73
206            <intent-filter>
206-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:30:13-32:29
207                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
207-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:31:17-81
207-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:31:25-78
208            </intent-filter>
209
210            <meta-data
210-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:37:13-39:40
211                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
211-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:38:17-92
212                android:value="true" />
212-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:39:17-37
213        </receiver>
214        <!--
215             FirebaseMessagingService performs security checks at runtime,
216             but set to not exported to explicitly avoid allowing another app to call it.
217        -->
218        <service
218-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:46:9-53:19
219            android:name="com.google.firebase.messaging.FirebaseMessagingService"
219-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:47:13-82
220            android:directBootAware="true"
220-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:48:13-43
221            android:exported="false" >
221-->[com.google.firebase:firebase-messaging:24.0.3] /Users/<USER>/.gradle/caches/transforms-3/fe33cd2a56a2323476c19e236c28d3ef/transformed/jetified-firebase-messaging-24.0.3/AndroidManifest.xml:49:13-37
222            <intent-filter android:priority="-500" >
222-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:21:13-23:29
223                <action android:name="com.google.firebase.MESSAGING_EVENT" />
223-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:22:17-78
223-->[:firebase_messaging] /Users/<USER>/StudioProjects/test/build/firebase_messaging/intermediates/merged_manifest/debug/AndroidManifest.xml:22:25-75
224            </intent-filter>
225        </service>
226
227        <activity
227-->[com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/87fac691493df4f79988be19c1e693b2/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:23:9-27:75
228            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
228-->[com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/87fac691493df4f79988be19c1e693b2/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:24:13-93
229            android:excludeFromRecents="true"
229-->[com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/87fac691493df4f79988be19c1e693b2/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:25:13-46
230            android:exported="false"
230-->[com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/87fac691493df4f79988be19c1e693b2/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:26:13-37
231            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
231-->[com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/87fac691493df4f79988be19c1e693b2/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:27:13-72
232        <!--
233            Service handling Google Sign-In user revocation. For apps that do not integrate with
234            Google Sign-In, this service will never be started.
235        -->
236        <service
236-->[com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/87fac691493df4f79988be19c1e693b2/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:33:9-37:51
237            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
237-->[com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/87fac691493df4f79988be19c1e693b2/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:34:13-89
238            android:exported="true"
238-->[com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/87fac691493df4f79988be19c1e693b2/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:35:13-36
239            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
239-->[com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/87fac691493df4f79988be19c1e693b2/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:36:13-107
240            android:visibleToInstantApps="true" />
240-->[com.google.android.gms:play-services-auth:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/87fac691493df4f79988be19c1e693b2/transformed/jetified-play-services-auth-21.0.0/AndroidManifest.xml:37:13-48
241
242        <activity
242-->[com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/transforms-3/44f3b8f078250072ad3e855c1c8d37b1/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:20:9-22:45
243            android:name="com.google.android.gms.common.api.GoogleApiActivity"
243-->[com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/transforms-3/44f3b8f078250072ad3e855c1c8d37b1/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:20:19-85
244            android:exported="false"
244-->[com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/transforms-3/44f3b8f078250072ad3e855c1c8d37b1/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:22:19-43
245            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
245-->[com.google.android.gms:play-services-base:18.0.1] /Users/<USER>/.gradle/caches/transforms-3/44f3b8f078250072ad3e855c1c8d37b1/transformed/jetified-play-services-base-18.0.1/AndroidManifest.xml:21:19-78
246
247        <provider
247-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/357784e7a16ccd95c68cfb70e35fa59d/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:23:9-28:39
248            android:name="com.google.firebase.provider.FirebaseInitProvider"
248-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/357784e7a16ccd95c68cfb70e35fa59d/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:24:13-77
249            android:authorities="com.AlKhabeer.firebaseinitprovider"
249-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/357784e7a16ccd95c68cfb70e35fa59d/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:25:13-72
250            android:directBootAware="true"
250-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/357784e7a16ccd95c68cfb70e35fa59d/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:26:13-43
251            android:exported="false"
251-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/357784e7a16ccd95c68cfb70e35fa59d/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:27:13-37
252            android:initOrder="100" />
252-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/transforms-3/357784e7a16ccd95c68cfb70e35fa59d/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:28:13-36
253        <provider
253-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/98a13afb1037d30c765555e4ae128327/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:24:9-32:20
254            android:name="androidx.startup.InitializationProvider"
254-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/98a13afb1037d30c765555e4ae128327/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:25:13-67
255            android:authorities="com.AlKhabeer.androidx-startup"
255-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/98a13afb1037d30c765555e4ae128327/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:26:13-68
256            android:exported="false" >
256-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/98a13afb1037d30c765555e4ae128327/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:27:13-37
257            <meta-data
257-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/98a13afb1037d30c765555e4ae128327/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
258                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
258-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/98a13afb1037d30c765555e4ae128327/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
259                android:value="androidx.startup" />
259-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/transforms-3/98a13afb1037d30c765555e4ae128327/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
260            <meta-data
260-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
261                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
261-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
262                android:value="androidx.startup" />
262-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
263        </provider>
264
265        <uses-library
265-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/1a8b478347169530bac076d1af4efc75/transformed/jetified-window-1.2.0/AndroidManifest.xml:23:9-25:40
266            android:name="androidx.window.extensions"
266-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/1a8b478347169530bac076d1af4efc75/transformed/jetified-window-1.2.0/AndroidManifest.xml:24:13-54
267            android:required="false" />
267-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/1a8b478347169530bac076d1af4efc75/transformed/jetified-window-1.2.0/AndroidManifest.xml:25:13-37
268        <uses-library
268-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/1a8b478347169530bac076d1af4efc75/transformed/jetified-window-1.2.0/AndroidManifest.xml:26:9-28:40
269            android:name="androidx.window.sidecar"
269-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/1a8b478347169530bac076d1af4efc75/transformed/jetified-window-1.2.0/AndroidManifest.xml:27:13-51
270            android:required="false" />
270-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/1a8b478347169530bac076d1af4efc75/transformed/jetified-window-1.2.0/AndroidManifest.xml:28:13-37
271
272        <meta-data
272-->[com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/transforms-3/6408c3c41b137332f20e3d3bc0428c8c/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:21:9-23:69
273            android:name="com.google.android.gms.version"
273-->[com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/transforms-3/6408c3c41b137332f20e3d3bc0428c8c/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:22:13-58
274            android:value="@integer/google_play_services_version" />
274-->[com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/transforms-3/6408c3c41b137332f20e3d3bc0428c8c/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:23:13-66
275
276        <receiver
276-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
277            android:name="androidx.profileinstaller.ProfileInstallReceiver"
277-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
278            android:directBootAware="false"
278-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
279            android:enabled="true"
279-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
280            android:exported="true"
280-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
281            android:permission="android.permission.DUMP" >
281-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
282            <intent-filter>
282-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
283                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
283-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
283-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
284            </intent-filter>
285            <intent-filter>
285-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
286                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
286-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
286-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
287            </intent-filter>
288            <intent-filter>
288-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
289                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
289-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
289-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
290            </intent-filter>
291            <intent-filter>
291-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
292                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
292-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
292-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/transforms-3/c0e8d1716ea866377b8ab916b7682a14/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
293            </intent-filter>
294        </receiver>
295
296        <service
296-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/96ffa47d0dd9b7416c80de5663a8865a/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:28:9-34:19
297            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
297-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/96ffa47d0dd9b7416c80de5663a8865a/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:29:13-103
298            android:exported="false" >
298-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/96ffa47d0dd9b7416c80de5663a8865a/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:30:13-37
299            <meta-data
299-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/96ffa47d0dd9b7416c80de5663a8865a/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:31:13-33:39
300                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
300-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/96ffa47d0dd9b7416c80de5663a8865a/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:32:17-94
301                android:value="cct" />
301-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/96ffa47d0dd9b7416c80de5663a8865a/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:33:17-36
302        </service>
303        <service
303-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/7309f79d0976d2d00ad66906f83b59f3/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:26:9-30:19
304            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
304-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/7309f79d0976d2d00ad66906f83b59f3/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:27:13-117
305            android:exported="false"
305-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/7309f79d0976d2d00ad66906f83b59f3/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:28:13-37
306            android:permission="android.permission.BIND_JOB_SERVICE" >
306-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/7309f79d0976d2d00ad66906f83b59f3/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:29:13-69
307        </service>
308
309        <receiver
309-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/7309f79d0976d2d00ad66906f83b59f3/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:32:9-34:40
310            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
310-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/7309f79d0976d2d00ad66906f83b59f3/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:33:13-132
311            android:exported="false" />
311-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/transforms-3/7309f79d0976d2d00ad66906f83b59f3/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:34:13-37
312    </application>
313
314</manifest>
