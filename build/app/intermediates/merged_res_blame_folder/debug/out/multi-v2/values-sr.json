{"logs": [{"outputFile": "com.AlKhabeer.app-mergeDebugResources-33:/values-sr/values-sr.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-3/62e4a06f332e44aa23e2085894e571ac/transformed/jetified-media3-exoplayer-1.4.1/res/values-sr/values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,190,255,326,404,476,563,646", "endColumns": "73,60,64,70,77,71,86,82,72", "endOffsets": "124,185,250,321,399,471,558,641,714"}, "to": {"startLines": "28,29,30,31,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3062,3136,3197,3262,3333,3411,3483,3570,3653", "endColumns": "73,60,64,70,77,71,86,82,72", "endOffsets": "3131,3192,3257,3328,3406,3478,3565,3648,3721"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/44f3b8f078250072ad3e855c1c8d37b1/transformed/jetified-play-services-base-18.0.1/res/values-sr/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,447,569,675,825,948,1056,1154,1299,1402,1558,1681,1826,1964,2028,2089", "endColumns": "101,151,121,105,149,122,107,97,144,102,155,122,144,137,63,60,75", "endOffsets": "294,446,568,674,824,947,1055,1153,1298,1401,1557,1680,1825,1963,2027,2088,2164"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "781,887,1043,1169,1279,1433,1560,1672,1904,2053,2160,2320,2447,2596,2738,2806,2871", "endColumns": "105,155,125,109,153,126,111,101,148,106,159,126,148,141,67,64,79", "endOffsets": "882,1038,1164,1274,1428,1555,1667,1769,2048,2155,2315,2442,2591,2733,2801,2866,2946"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/a3de342a3f81e3197d28e19f8e01251f/transformed/core-1.13.1/res/values-sr/values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "2,3,4,5,6,7,8,40", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,4037", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,4133"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/6408c3c41b137332f20e3d3bc0428c8c/transformed/jetified-play-services-basement-18.3.0/res/values-sr/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "125", "endOffsets": "320"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1774", "endColumns": "129", "endOffsets": "1899"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/86ee45f5c791d8d00fa99ae2b84aaec9/transformed/browser-1.8.0/res/values-sr/values-sr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,166,266,379", "endColumns": "110,99,112,97", "endOffsets": "161,261,374,472"}, "to": {"startLines": "27,37,38,39", "startColumns": "4,4,4,4", "startOffsets": "2951,3726,3826,3939", "endColumns": "110,99,112,97", "endOffsets": "3057,3821,3934,4032"}}]}]}