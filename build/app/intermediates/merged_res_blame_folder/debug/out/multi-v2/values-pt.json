{"logs": [{"outputFile": "com.AlKhabeer.app-mergeDebugResources-33:/values-pt/values-pt.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-3/a3de342a3f81e3197d28e19f8e01251f/transformed/core-1.13.1/res/values-pt/values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,670,790", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "147,249,348,448,555,665,785,886"}, "to": {"startLines": "2,3,4,5,6,7,8,22", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,670,1908", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "147,249,348,448,555,665,785,2004"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/86ee45f5c791d8d00fa99ae2b84aaec9/transformed/browser-1.8.0/res/values-pt/values-pt.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,170,269,381", "endColumns": "114,98,111,105", "endOffsets": "165,264,376,482"}, "to": {"startLines": "9,19,20,21", "startColumns": "4,4,4,4", "startOffsets": "790,1591,1690,1802", "endColumns": "114,98,111,105", "endOffsets": "900,1685,1797,1903"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/62e4a06f332e44aa23e2085894e571ac/transformed/jetified-media3-exoplayer-1.4.1/res/values-pt/values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,195,267,333,410,477,578,671", "endColumns": "69,69,71,65,76,66,100,92,69", "endOffsets": "120,190,262,328,405,472,573,666,736"}, "to": {"startLines": "10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "905,975,1045,1117,1183,1260,1327,1428,1521", "endColumns": "69,69,71,65,76,66,100,92,69", "endOffsets": "970,1040,1112,1178,1255,1322,1423,1516,1586"}}]}]}