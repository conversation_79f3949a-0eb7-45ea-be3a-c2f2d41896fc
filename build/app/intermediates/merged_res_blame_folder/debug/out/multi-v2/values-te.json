{"logs": [{"outputFile": "com.AlKhabeer.app-mergeDebugResources-36:/values-te/values-te.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-3/62e4a06f332e44aa23e2085894e571ac/transformed/jetified-media3-exoplayer-1.4.1/res/values-te/values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,195,268,336,416,493,594,687", "endColumns": "71,67,72,67,79,76,100,92,77", "endOffsets": "122,190,263,331,411,488,589,682,760"}, "to": {"startLines": "55,56,57,58,59,60,61,62,63", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "5896,5968,6036,6109,6177,6257,6334,6435,6528", "endColumns": "71,67,72,67,79,76,100,92,77", "endOffsets": "5963,6031,6104,6172,6252,6329,6430,6523,6601"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/6408c3c41b137332f20e3d3bc0428c8c/transformed/jetified-play-services-basement-18.3.0/res/values-te/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "133", "endOffsets": "328"}, "to": {"startLines": "44", "startColumns": "4", "startOffsets": "4571", "endColumns": "137", "endOffsets": "4704"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/a3de342a3f81e3197d28e19f8e01251f/transformed/core-1.13.1/res/values-te/values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,265,367,468,574,681,805", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "152,260,362,463,569,676,800,901"}, "to": {"startLines": "29,30,31,32,33,34,35,68", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2845,2947,3055,3157,3258,3364,3471,7013", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "2942,3050,3152,3253,3359,3466,3590,7109"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/86ee45f5c791d8d00fa99ae2b84aaec9/transformed/browser-1.8.0/res/values-te/values-te.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,166,274,385", "endColumns": "110,107,110,106", "endOffsets": "161,269,380,487"}, "to": {"startLines": "54,64,65,66", "startColumns": "4,4,4,4", "startOffsets": "5785,6606,6714,6825", "endColumns": "110,107,110,106", "endOffsets": "5891,6709,6820,6927"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/953d05e3fa95b7ff82a1fd5ab5bbc38e/transformed/appcompat-1.2.0/res/values-te/values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,334,445,535,640,759,837,913,1004,1097,1192,1286,1386,1479,1574,1669,1760,1851,1934,2048,2150,2247,2362,2465,2580,2742,2845", "endColumns": "116,111,110,89,104,118,77,75,90,92,94,93,99,92,94,94,90,90,82,113,101,96,114,102,114,161,102,80", "endOffsets": "217,329,440,530,635,754,832,908,999,1092,1187,1281,1381,1474,1569,1664,1755,1846,1929,2043,2145,2242,2357,2460,2575,2737,2840,2921"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,334,445,535,640,759,837,913,1004,1097,1192,1286,1386,1479,1574,1669,1760,1851,1934,2048,2150,2247,2362,2465,2580,2742,6932", "endColumns": "116,111,110,89,104,118,77,75,90,92,94,93,99,92,94,94,90,90,82,113,101,96,114,102,114,161,102,80", "endOffsets": "217,329,440,530,635,754,832,908,999,1092,1187,1281,1381,1474,1569,1664,1755,1846,1929,2043,2145,2242,2357,2460,2575,2737,2840,7008"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/44f3b8f078250072ad3e855c1c8d37b1/transformed/jetified-play-services-base-18.0.1/res/values-te/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,299,449,575,686,819,940,1041,1137,1282,1390,1539,1667,1814,1973,2033,2099", "endColumns": "105,149,125,110,132,120,100,95,144,107,148,127,146,158,59,65,77", "endOffsets": "298,448,574,685,818,939,1040,1136,1281,1389,1538,1666,1813,1972,2032,2098,2176"}, "to": {"startLines": "36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3595,3705,3859,3989,4104,4241,4366,4471,4709,4858,4970,5123,5255,5406,5569,5633,5703", "endColumns": "109,153,129,114,136,124,104,99,148,111,152,131,150,162,63,69,81", "endOffsets": "3700,3854,3984,4099,4236,4361,4466,4566,4853,4965,5118,5250,5401,5564,5628,5698,5780"}}]}]}