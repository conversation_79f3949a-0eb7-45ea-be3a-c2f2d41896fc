name: al_khabeer
description: A new Flutter project.

# The following line prevents the package from being accidentally published to
# pub.dev using `pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 1.0.8+10

environment:
  sdk: ">=3.0.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  flutter_web_plugins:
    sdk: flutter
  auto_size_text: 3.0.0
  cloud_firestore: 5.5.0
  collection: 1.19.1
  data_table_2: 2.5.10
  emoji_flag_converter: 1.1.0
  file_picker: 8.1.2
  firebase_core: 3.8.0
  firebase_messaging:
  floating_bottom_navigation_bar: 1.5.2
  flutter_animate: 4.5.0
  font_awesome_flutter: 10.7.0
  from_css_color: 2.0.0
  go_router: 12.1.3
  google_fonts: 6.1.0
  google_sign_in: 6.2.1
  image_picker: 1.1.2
  intl: 0.19.0
  json_path: 0.7.2
  mime_type: 1.0.0
  page_transition: 2.1.0
  photo_view: 0.15.0
  provider: 6.1.2
  rxdart: 0.27.7
  shared_preferences: 2.3.2
  supabase: 2.3.0
  supabase_flutter: 2.6.0
  table_calendar: 3.1.1
  timeago: 3.6.1
  url_launcher: 6.3.0
  video_player: 2.9.2

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.0

dependency_overrides:
  http: 1.2.2
  rxdart: 0.27.7
  uuid: ^4.0.0
  win32: 5.5.1

dev_dependencies:
  flutter_launcher_icons: 0.13.1
  flutter_lints: 4.0.0
  image: 4.2.0
  lints: 4.0.0
  flutter_test:
    sdk: flutter


flutter_launcher_icons:
  android: 'launcher_icon'
  ios: true
  web:
    generate: true
  image_path: 'assets/images/app_launcher_icon.png'
  adaptive_icon_background: '#1e1e1e'
  adaptive_icon_foreground: 'assets/images/adaptive_foreground_icon.png'


# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/fonts/
    - assets/images/
    - assets/videos/
    - assets/audios/
    - assets/rive_animations/
    - assets/pdfs/
    - assets/jsons/



  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages

